<?php
// Exit if accessed directly
if ( !defined( 'ABSPATH' ) ) exit;

// BEGIN ENQUEUE PARENT ACTION
// AUTO GENERATED - Do not modify or remove comment markers above or below:

if ( !function_exists( 'chld_thm_cfg_locale_css' ) ):
    function chld_thm_cfg_locale_css( $uri ){
        if ( empty( $uri ) && is_rtl() && file_exists( get_template_directory() . '/rtl.css' ) )
            $uri = get_template_directory_uri() . '/rtl.css';
        return $uri;
    }
endif;
add_filter( 'locale_stylesheet_uri', 'chld_thm_cfg_locale_css' );

if ( !function_exists( 'child_theme_configurator_css' ) ):
    function child_theme_configurator_css() {
        wp_enqueue_style( 'chld_thm_cfg_child', trailingslashit( get_stylesheet_directory_uri() ) . 'style.css', array( 'hello-elementor','hello-elementor','hello-elementor-theme-style','hello-elementor-header-footer' ) );
    }
endif;
add_action( 'wp_enqueue_scripts', 'child_theme_configurator_css', 20 );
function enqueue_bootstrap_scripts() {
    // Enqueue Bootstrap CSS
    wp_enqueue_style(
        'bootstrap-css',
        'https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css',
        array(),
        '4.5.2'
    );

    // Enqueue jQuery (slim version, if needed)
//    wp_enqueue_script(
//        'jquery-slim',
//        'https://code.jquery.com/jquery-3.5.1.slim.min.js',
//        array(),
//        '3.5.1',
//        true
//    );
    // Enqueue Popper.js (required for Bootstrap components like dropdowns)
    wp_enqueue_script(
        'popper-js',
        'https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js',
        array(),
        '2.5.2',
        true
    );

    // Enqueue Bootstrap JS
    wp_enqueue_script(
        'bootstrap-js',
        'https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.min.js',
        array('jquery-slim', 'popper-js'),
        '4.5.2',
        true
    );
}
add_action('wp_enqueue_scripts', 'enqueue_bootstrap_scripts');
// END ENQUEUE PARENT ACTION

// add single product page class


// Code for Meet Our Partners custom post type

function register_meet_our_partners_cpt() {
    $labels = array(
        'name' => 'Meet Our Partners',
        'singular_name' => 'Partner',
        'add_new' => 'Add New Partner',
        'add_new_item' => 'Add New Partner',
        'edit_item' => 'Edit Partner',
        'new_item' => 'New Partner',
        'view_item' => 'View Partner',
        'search_items' => 'Search Partners',
        'not_found' => 'No partners found',
        'menu_name' => 'Our Partners'
    );

    $args = array(
        'label' => 'Meet Our Partners',
        'labels' => $labels,
        'public' => true,
        'menu_icon' => 'dashicons-groups',
        'supports' => array('title', 'thumbnail'),
        'has_archive' => true,
        'show_in_rest' => true,
    );

    register_post_type('meet_our_partners', $args);
}
add_action('init', 'register_meet_our_partners_cpt');

//product categories repeater
function add_partner_section_metabox() {
    add_meta_box(
        'partner_info_sections',
        'Product Categories / Lineup / Key Features / Product Offerings / Frequently Asked Questions',
        'partner_section_metabox_callback',
        'meet_our_partners',
        'normal',
        'default'
    );
}
add_action('add_meta_boxes', 'add_partner_section_metabox');
function partner_section_metabox_callback($post) {
    $main_heading = get_post_meta($post->ID, '_partner_section_heading', true);
    $repeater_data = get_post_meta($post->ID, '_partner_section_repeater', true);
    if (!is_array($repeater_data)) $repeater_data = [];
    ?>
    <p>
        <label><strong>Section Title:</strong></label><br>
        <input type="text" name="partner_section_heading" value="<?php echo esc_attr($main_heading); ?>" style="width:100%;">
    </p>

    <hr>
    <div id="partner-section-repeater-wrapper">
        <?php foreach ($repeater_data as $index => $item): ?>
            <div class="section-repeater-block" style="border:1px solid #ccc; padding:10px; margin-bottom:10px;">
                <input type="text" name="partner_section_repeater[<?php echo $index; ?>][heading]" placeholder="Heading" value="<?php echo esc_attr($item['heading']); ?>" style="width:100%; margin-bottom:5px;" />
                <textarea name="partner_section_repeater[<?php echo $index; ?>][text]" placeholder="Text" style="width:100%; height:80px;"><?php echo esc_textarea($item['text']); ?></textarea>
                <button type="button" class="remove-section-row button">Remove</button>
            </div>
        <?php endforeach; ?>
    </div>

    <button type="button" id="add-partner-section-row" class="button">Add Row</button>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            let wrapper = document.getElementById('partner-section-repeater-wrapper');
            let addBtn = document.getElementById('add-partner-section-row');
            let index = <?php echo count($repeater_data); ?>;

            addBtn.addEventListener('click', function () {
                let block = document.createElement('div');
                block.className = 'section-repeater-block';
                block.style.cssText = 'border:1px solid #ccc; padding:10px; margin-bottom:10px;';
                block.innerHTML = `
                    <input type="text" name="partner_section_repeater[${index}][heading]" placeholder="Heading" style="width:100%; margin-bottom:5px;" />
                    <textarea name="partner_section_repeater[${index}][text]" placeholder="Text" style="width:100%; height:80px;"></textarea>
                    <button type="button" class="remove-section-row button">Remove</button>
                `;
                wrapper.appendChild(block);
                index++;
            });

            wrapper.addEventListener('click', function (e) {
                if (e.target.classList.contains('remove-section-row')) {
                    e.target.closest('.section-repeater-block').remove();
                }
            });
        });
    </script>
    <?php
}
function save_partner_section_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;

    if (isset($_POST['partner_section_heading'])) {
        update_post_meta($post_id, '_partner_section_heading', sanitize_text_field($_POST['partner_section_heading']));
    }

    if (isset($_POST['partner_section_repeater']) && is_array($_POST['partner_section_repeater'])) {
        $cleaned = [];
        foreach ($_POST['partner_section_repeater'] as $row) {
            if (!empty($row['heading']) || !empty($row['text'])) {
                $cleaned[] = [
                    'heading' => sanitize_text_field($row['heading']),
                    'text' => sanitize_textarea_field($row['text']),
                ];
            }
        }
        update_post_meta($post_id, '_partner_section_repeater', $cleaned);
    } else {
        delete_post_meta($post_id, '_partner_section_repeater');
    }
}
add_action('save_post', 'save_partner_section_meta');
$main_heading = get_post_meta(get_the_ID(), '_partner_section_heading', true);
$sections = get_post_meta(get_the_ID(), '_partner_section_repeater', true);

if ($main_heading) {
    echo '<h2>' . esc_html($main_heading) . '</h2>';
}

if (!empty($sections)) {
    foreach ($sections as $row) {
        echo '<h4>' . esc_html($row['heading']) . '</h4>';
        echo '<p>' . esc_html($row['text']) . '</p>';
    }
}
function render_partner_sections_shortcode($atts) {
    $post_id = get_the_ID();
    if (!empty($atts['id'])) {
        $post_id = intval($atts['id']);
    }
    if (!$post_id) return '';

    $main_heading = get_post_meta($post_id, '_partner_section_heading', true);
    $sections = get_post_meta($post_id, '_partner_section_repeater', true);
    if (empty($main_heading) && empty($sections)) return '';

    ob_start();
    echo '<div class="partner-section-block">';
    if (!empty($main_heading)) {
        echo '<h2 class="partner-main-heading">' . esc_html($main_heading) . '</h2>';
    }
    echo '<div class="partner-section-item_wrapper">';
    if (!empty($sections)) {
        foreach ($sections as $row) {

            echo '<div class="partner-section-item">';
            echo '<h4>' . esc_html($row['heading']) . '</h4>';
            echo '<p>' . nl2br(esc_html($row['text'])) . '</p>';
            echo '</div>';
        }
    }
    echo '</div>';
    echo '</div>';
    return ob_get_clean();
}
add_shortcode('partner_sections', 'render_partner_sections_shortcode');

//

//flaver/key features wrapper
// Register metabox
function add_image_text_metabox() {
    add_meta_box(
        'image_text_section',
        'Key Features',
        'image_text_metabox_callback',
        'meet_our_partners',
        'normal',
        'default'
    );
}
add_action('add_meta_boxes', 'add_image_text_metabox');

function image_text_metabox_callback($post) {
    $sections = get_post_meta($post->ID, '_image_text_sections', true);
    if (!is_array($sections)) $sections = [];
    ?>
    <div id="outer-repeater-wrapper">
        <?php foreach ($sections as $s_index => $section): ?>
            <div class="outer-repeater-section" style="border:2px solid #333; padding:10px; margin-bottom:15px;">
                <p>
                    <label><strong>Section Title:</strong></label><br>
                    <input type="text" name="image_text_sections[<?php echo $s_index; ?>][section_title]" value="<?php echo esc_attr($section['section_title']); ?>" style="width:100%;">
                </p>

                <div class="inner-repeater-wrapper">
                    <?php
                    $inner = isset($section['items']) && is_array($section['items']) ? $section['items'] : [];
                    foreach ($inner as $i_index => $item): ?>
                        <div class="image_text_repeater" style="border:1px solid #ccc; padding:10px; margin-bottom:10px;">
                            <div class="heading_text_repeater_wrap">
                                <h4><input type="text" name="image_text_sections[<?php echo $s_index; ?>][items][<?php echo $i_index; ?>][heading]" value="<?php echo esc_attr($item['heading']); ?>" placeholder="Heading" style="width:100%;" /></h4>
                                <textarea name="image_text_sections[<?php echo $s_index; ?>][items][<?php echo $i_index; ?>][text]" style="width:100%; height:80px;" placeholder="Text"><?php echo esc_textarea($item['text']); ?></textarea>
                            </div>
                            <div class="imager_repeater">
                                <input type="text" name="image_text_sections[<?php echo $s_index; ?>][items][<?php echo $i_index; ?>][image]" value="<?php echo esc_url($item['image']); ?>" placeholder="Image URL" style="width:100%;" />
                            </div>
                            <button type="button" class="remove-image-text-row button">Remove Inner</button>
                        </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" class="add-inner-row button" data-index="<?php echo $s_index; ?>">Add Inner Row</button>
                <button type="button" class="remove-outer-row button">Remove Section</button>
            </div>
        <?php endforeach; ?>
    </div>

    <button type="button" id="add-outer-row" class="button button-primary">Add Section</button>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            let outerWrapper = document.getElementById('outer-repeater-wrapper');
            let addOuterBtn = document.getElementById('add-outer-row');
            let outerIndex = <?php echo count($sections); ?>;

            addOuterBtn.addEventListener('click', function () {
                let outerDiv = document.createElement('div');
                outerDiv.className = 'outer-repeater-section';
                outerDiv.style.cssText = 'border:2px solid #333; padding:10px; margin-bottom:15px;';
                outerDiv.innerHTML = `
                    <p>
                        <label><strong>Section Title:</strong></label><br>
                        <input type="text" name="image_text_sections[${outerIndex}][section_title]" style="width:100%;">
                    </p>
                    <div class="inner-repeater-wrapper"></div>
                    <button type="button" class="add-inner-row button" data-index="${outerIndex}">Add Inner Row</button>
                    <button type="button" class="remove-outer-row button">Remove Section</button>
                `;
                outerWrapper.appendChild(outerDiv);
                outerIndex++;
            });

            // Handle adding inner row
            document.addEventListener('click', function (e) {
                if (e.target.classList.contains('add-inner-row')) {
                    const outerIndex = e.target.getAttribute('data-index');
                    const wrapper = e.target.previousElementSibling;
                    const count = wrapper.children.length;
                    const div = document.createElement('div');
                    div.className = 'image_text_repeater';
                    div.style.cssText = 'border:1px solid #ccc; padding:10px; margin-bottom:10px;';
                    div.innerHTML = `
                        <div class="heading_text_repeater_wrap">
                            <h4><input type="text" name="image_text_sections[${outerIndex}][items][${count}][heading]" placeholder="Heading" style="width:100%;" /></h4>
                            <textarea name="image_text_sections[${outerIndex}][items][${count}][text]" placeholder="Text" style="width:100%; height:80px;"></textarea>
                        </div>
                        <div class="imager_repeater">
                            <input type="text" name="image_text_sections[${outerIndex}][items][${count}][image]" placeholder="Image URL" style="width:100%;" />
                        </div>
                        <button type="button" class="remove-image-text-row button">Remove Inner</button>
                    `;
                    wrapper.appendChild(div);
                }

                if (e.target.classList.contains('remove-image-text-row')) {
                    e.target.closest('.image_text_repeater').remove();
                }

                if (e.target.classList.contains('remove-outer-row')) {
                    e.target.closest('.outer-repeater-section').remove();
                }
            });
        });
    </script>
    <?php
}

// Save Meta
function save_image_text_repeater_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;

    if (isset($_POST['image_text_sections']) && is_array($_POST['image_text_sections'])) {
        $cleaned = [];

        foreach ($_POST['image_text_sections'] as $section) {
            if (empty($section['section_title']) && empty($section['items'])) continue;

            $items = [];
            if (isset($section['items']) && is_array($section['items'])) {
                foreach ($section['items'] as $item) {
                    if (!empty($item['heading']) || !empty($item['text']) || !empty($item['image'])) {
                        $items[] = [
                            'heading' => sanitize_text_field($item['heading']),
                            'text' => sanitize_textarea_field($item['text']),
                            'image' => esc_url_raw($item['image']),
                        ];
                    }
                }
            }

            $cleaned[] = [
                'section_title' => sanitize_text_field($section['section_title']),
                'items' => $items,
            ];
        }

        update_post_meta($post_id, '_image_text_sections', $cleaned);
    } else {
        delete_post_meta($post_id, '_image_text_sections');
    }
}
add_action('save_post', 'save_image_text_repeater_meta');

// Shortcode to display sections
function render_image_text_repeater_shortcode($atts) {
    $post_id = get_the_ID();
    if (!empty($atts['id'])) $post_id = intval($atts['id']);
    if (!$post_id) return '';

    $sections = get_post_meta($post_id, '_image_text_sections', true);
    if (empty($sections)) return '';

    ob_start();
    echo '<div class="image-text-section-block">';
    foreach ($sections as $section) {
        if (!empty($section['section_title'])) {
            echo '<h2 class="image-text-main-heading">' . esc_html($section['section_title']) . '</h2>';
        }

        if (!empty($section['items'])) {
            echo '<div class="image-text-section-wrapper">';
            foreach ($section['items'] as $item) {
                echo '<div class="image-text-item">';
                if (!empty($item['heading'])) echo '<h4>' . esc_html($item['heading']) . '</h4>';
                if (!empty($item['text'])) echo '<p>' . nl2br(esc_html($item['text'])) . '</p>';
                if (!empty($item['image'])) echo '<img src="' . esc_url($item['image']) . '" alt="">';
                echo '</div>';
            }
            echo '</div>';
        }
    }
    echo '</div>';
    return ob_get_clean();
}
add_shortcode('image_text_sections', 'render_image_text_repeater_shortcode');


//
//imager upload and text heading repater
function add_partner_image_text_repeater_metabox() {
    add_meta_box(
        'partner_image_text_repeater',
        'Image & Text Blocks',
        'partner_image_text_repeater_callback',
        'meet_our_partners',
        'normal',
        'default'
    );
}
add_action('add_meta_boxes', 'add_partner_image_text_repeater_metabox');
function partner_image_text_repeater_callback($post) {
    $repeater_data = get_post_meta($post->ID, '_partner_image_text_repeater', true);
    if (!is_array($repeater_data)) $repeater_data = [];
    ?>
    <div id="image-text-repeater-wrapper">
        <?php foreach ($repeater_data as $index => $item): ?>

            <div class="image_text_repeater" style="">
                <div class="heading_text_repeater_wrap" style="margin-bottom:10px;">
                    <input type="text" name="partner_image_text_repeater[<?php echo $index; ?>][heading]" placeholder="Heading" value="<?php echo esc_attr($item['heading']); ?>" style="width:100%; margin-bottom:5px;" />
                    <textarea name="partner_image_text_repeater[<?php echo $index; ?>][text]" placeholder="Text" style="width:100%; height:80px;"><?php echo esc_textarea($item['text']); ?></textarea>
                </div>
                <div class="imager_repeater">
                    <div>
                        <?php $image_url = !empty($item['image']) ? esc_url($item['image']) : ''; ?>
                        <img src="<?php echo $image_url; ?>" style="max-width: 150px; display:block; margin-bottom:5px;" />
                        <input type="hidden" name="partner_image_text_repeater[<?php echo $index; ?>][image]" value="<?php echo $image_url; ?>" />
                        <button type="button" class="upload-image button">Upload Image</button>
                        <button type="button" class="remove-section-row button">Remove</button>
                    </div>
                </div>
            </div>


        <?php endforeach; ?>
    </div>

    <button type="button" id="add-image-text-row" class="button">Add Row</button>

    <script>
        jQuery(document).ready(function($) {
            let wrapper = $('#image-text-repeater-wrapper');
            let index = <?php echo count($repeater_data); ?>;

            $('#add-image-text-row').on('click', function() {
                let block = `
                    <div class="image_text_repeater" style="margin-bottom:20px; border:1px solid #ccc; padding:10px;">
                        <div class="heading_text_repeater_wrap" style="margin-bottom:10px;">
                            <input type="text" name="partner_image_text_repeater[${index}][heading]" placeholder="Heading" style="width:100%; margin-bottom:5px;" />
                            <textarea name="partner_image_text_repeater[${index}][text]" placeholder="Text" style="width:100%; height:80px;"></textarea>
                        </div>
                        <div class="imager_repeater">
                            <div>
                                <img src="" style="max-width:150px; display:block; margin-bottom:5px;" />
                                <input type="hidden" name="partner_image_text_repeater[${index}][image]" value="" />
                                <button type="button" class="upload-image button">Upload Image</button>
                                <button type="button" class="remove-section-row button">Remove</button>
                            </div>
                        </div>
                    </div>`;
                wrapper.append(block);
                index++;
            });

            wrapper.on('click', '.remove-section-row', function() {
                $(this).closest('.image_text_repeater').remove();
            });

            wrapper.on('click', '.upload-image', function(e) {
                e.preventDefault();
                let button = $(this);
                let img = button.prevAll('img');
                let input = button.prevAll('input[type="hidden"]');

                let frame = wp.media({
                    title: 'Select or Upload Image',
                    button: { text: 'Use this image' },
                    multiple: false
                });

                frame.on('select', function() {
                    let attachment = frame.state().get('selection').first().toJSON();
                    img.attr('src', attachment.url);
                    input.val(attachment.url);
                });

                frame.open();
            });
        });
    </script>
    <?php
}
function save_partner_image_text_repeater_data($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;

    if (isset($_POST['partner_image_text_repeater']) && is_array($_POST['partner_image_text_repeater'])) {
        $cleaned = [];
        foreach ($_POST['partner_image_text_repeater'] as $row) {
            if (!empty($row['heading']) || !empty($row['text']) || !empty($row['image'])) {
                $cleaned[] = [
                    'heading' => sanitize_text_field($row['heading']),
                    'text' => sanitize_textarea_field($row['text']),
                    'image' => esc_url_raw($row['image']),
                ];
            }
        }
        update_post_meta($post_id, '_partner_image_text_repeater', $cleaned);
    } else {
        delete_post_meta($post_id, '_partner_image_text_repeater');
    }
}
add_action('save_post', 'save_partner_image_text_repeater_data');

$data = get_post_meta(get_the_ID(), '_partner_image_text_repeater', true);
if (!empty($data)) {
    foreach ($data as $item) {
        echo '<div class="image_text_repeater">';
        echo '<div class="heading_text_repeater_wrap">';
        echo '<h4>' . esc_html($item['heading']) . '</h4>';
        echo '<p>' . esc_html($item['text']) . '</p>';
        echo '</div>';

        echo '<div class="imager_repeater">';
        if (!empty($item['image'])) {
            echo '<div><img src="' . esc_url($item['image']) . '" style="max-width:150px;"></div>';
        }

        echo '</div>';
        echo '</div>';
    }
}
function display_partner_image_text_repeater_shortcode($atts) {
    // Attributes (optional: allow post ID override)
    $atts = shortcode_atts([
        'id' => get_the_ID(),
    ], $atts);

    $post_id = $atts['id'];
    $data = get_post_meta($post_id, '_partner_image_text_repeater', true);

    if (empty($data) || !is_array($data)) return '';

    ob_start(); // Start capturing HTML output
    echo '<div class="partner-image-text-repeater-wrapper">';

    foreach ($data as $item) {
        echo '<div class="image_text_outer">';
        echo '<div class="image_text_repeater" style="">';

        // Heading + Text
        echo '<div class="heading_text_repeater_wrap" style="">';
        if (!empty($item['heading'])) {
            echo '<h4>' . esc_html($item['heading']) . '</h4>';
        }
        if (!empty($item['text'])) {
            echo '<p>' . esc_html($item['text']) . '</p>';
        }
        echo '</div>';

        // Image
        if (!empty($item['image'])) {
            echo '<div class="imager_repeater_wrapper">';
            echo '<div class="imager_repeater">';
            echo '<img src="' . esc_url($item['image']) . '" alt="" style="">';
            echo '</div>';
            echo '</div>';
        }

        echo '</div>'; // .image_text_repeater
        echo '</div>';
    }

    echo '</div>'; // .partner-image-text-repeater-wrapper
    return ob_get_clean();
}
add_shortcode('partner_image_text_repeater', 'display_partner_image_text_repeater_shortcode');

//
function add_partner_meta_boxes() {
    add_meta_box('partner_details', 'Partner Details', 'partner_meta_box_callback', 'meet_our_partners', 'normal', 'high');
}
add_action('add_meta_boxes', 'add_partner_meta_boxes');

function partner_meta_box_callback($post) {
    $email = get_post_meta($post->ID, '_partner_email', true);
    $instagram = get_post_meta($post->ID, '_partner_instagram', true);
    $facebook = get_post_meta($post->ID, '_partner_facebook', true);
    $whatsapp = get_post_meta($post->ID, '_partner_whatsapp', true);
    ?>
    <p><label>Email Address:</label><br />
        <input type="email" name="partner_email" value="<?php echo esc_attr($email); ?>" style="width:100%;" /></p>

    <p><label>Instagram URL:</label><br />
        <input type="url" name="partner_instagram" value="<?php echo esc_url($instagram); ?>" style="width:100%;" /></p>

    <p><label>Facebook URL:</label><br />
        <input type="url" name="partner_facebook" value="<?php echo esc_url($facebook); ?>" style="width:100%;" /></p>

    <p><label>WhatsApp Number (include country code):</label><br />
        <input type="text" name="partner_whatsapp" value="<?php echo esc_attr($whatsapp); ?>" style="width:100%;" /></p>
    <?php
}

function save_partner_meta_boxes($post_id) {
    if (array_key_exists('partner_email', $_POST)) {
        update_post_meta($post_id, '_partner_email', sanitize_email($_POST['partner_email']));
    }
    if (array_key_exists('partner_instagram', $_POST)) {
        update_post_meta($post_id, '_partner_instagram', esc_url_raw($_POST['partner_instagram']));
    }
    if (array_key_exists('partner_facebook', $_POST)) {
        update_post_meta($post_id, '_partner_facebook', esc_url_raw($_POST['partner_facebook']));
    }
    if (array_key_exists('partner_whatsapp', $_POST)) {
        update_post_meta($post_id, '_partner_whatsapp', sanitize_text_field($_POST['partner_whatsapp']));
    }
}
add_action('save_post', 'save_partner_meta_boxes');

// add_action('wpforms_process_complete', 'register_supplier_from_wpform', 10, 4);
add_action('wpforms_process_complete_436', 'register_supplier_from_wpform', 10, 4);

function register_supplier_from_wpform($fields, $entry, $form_data, $entry_id) {
    // (1) Map WPForm field values
    $name            = $fields[1]['value']; // Individual Name
    $email           = $fields[7]['value']; // Email
    $business_name   = $fields[8]['value']; // Business Name
    $resale_license  = $fields[9]['value']; // Resale License
    $description     = $fields[11]['value']; // Product Description
    $raw = isset($fields[10]['value']) ? $fields[10]['value'] : [];

    if (is_array($raw)) {
        $categories = $raw;
    } else {
        $categories = preg_split('/[\r\n,]+/', $raw);
        $categories = array_map('trim', $categories);
        $categories = array_filter($categories);
    }

    // (2) Create WP user account
     $username = sanitize_user(strtolower(str_replace(' ', '', $name)));
     $password = wp_generate_password();

     $user_id = wp_create_user($username, $password, $email);

    if (!is_wp_error($user_id)) {
        wp_update_user(['ID' => $user_id, 'role' => 'vendor']); // ✅ Assign vendor role

        update_user_meta($user_id, 'business_name', $business_name);
        update_user_meta($user_id, 'resale_license', $resale_license);
        update_user_meta($user_id, 'supplier_description', $description);
        update_user_meta($user_id, '_wcfm_vendor_type', 'default');

        // (3) Generate unique vendor code like 1c2-1, 1c2-2, etc
        $existing = new WP_Query([
            'post_type'      => 'vendor',
            'post_status'    => ['publish', 'draft'],
            'posts_per_page' => -1,
            'meta_query'     => [
                [
                    'key'     => '_vendor_code',
                    'value'   => 'CC-',
                    'compare' => 'LIKE'
                ]
            ],
            'fields' => 'ids'
        ]);
        $count = count($existing->posts);
        $vendor_code = 'CC-' . (1000 + $count);


        // (4) Create Vendor CPT post
        $vendor_post_id = wp_insert_post([
            'post_type'    => 'vendor',
            'post_status'  => 'draft',
            'post_title'   => $business_name,
        ]);

        if ($vendor_post_id && !is_wp_error($vendor_post_id)) {
            update_post_meta($vendor_post_id, '_individual_name', $name);
            update_post_meta($vendor_post_id, '_business_name', $business_name);
            update_post_meta($vendor_post_id, '_email', $email);
            update_post_meta($vendor_post_id, '_resale_license', $resale_license);
            update_post_meta($vendor_post_id, '_product_description', $description);
            update_post_meta($vendor_post_id, '_supplier_categories', $categories);

            update_post_meta($vendor_post_id, '_vendor_code', $vendor_code);
        }

        // (5) Email the new vendor
        $headers = [
            'From: Your Site <<EMAIL>>',
            'Content-Type: text/plain; charset=UTF-8'
        ];

        $message = "Hi $name,\n\nYour vendor account has been created." . wp_login_url();

        wp_mail($email, 'Your Vendor Account Created', $message, $headers);
    }
}
// Register 'vendor' role if not exists
function ensure_vendor_role_exists() {
    if (!get_role('vendor')) {
        add_role('vendor', 'Vendor', [
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
        ]);
    }
}
add_action('init', 'ensure_vendor_role_exists');

//1️⃣ Add custom bulk action to vendor post list
add_filter('bulk_actions-edit-vendor', 'add_vendor_bulk_publish_action');
function add_vendor_bulk_publish_action($bulk_actions) {
    $bulk_actions['publish_vendors'] = 'Publish Vendors';
    return $bulk_actions;
}

// 2️⃣ Handle the bulk action logic
add_filter('handle_bulk_actions-edit-vendor', 'handle_vendor_bulk_publish_action', 10, 3);
function handle_vendor_bulk_publish_action($redirect_to, $doaction, $post_ids) {
    if ($doaction !== 'publish_vendors') {
        return $redirect_to;
    }

    $published_count = 0;

    foreach ($post_ids as $post_id) {
        if (get_post_status($post_id) !== 'publish') {
            wp_update_post([
                'ID' => $post_id,
                'post_status' => 'publish'
            ]);
            $published_count++;
        }
    }

    // Redirect back with a notice
    $redirect_to = add_query_arg('published_vendors', $published_count, $redirect_to);
    return $redirect_to;
}

// 3️⃣ Show admin notice after bulk publish
add_action('admin_notices', 'vendor_bulk_publish_admin_notice');
function vendor_bulk_publish_admin_notice() {
    if (!empty($_REQUEST['published_vendors'])) {
        $count = intval($_REQUEST['published_vendors']);
        printf(
            '<div id="message" class="updated notice notice-success is-dismissible"><p>%s vendor(s) published.</p></div>',
            $count
        );
    }
}

// Add custom column to Vendor admin list
add_filter('manage_vendor_posts_columns', 'add_vendor_code_column');
function add_vendor_code_column($columns) {
    $columns['vendor_code'] = 'Vendor Code';
    return $columns;
}

// Populate the custom column
add_action('manage_vendor_posts_custom_column', 'show_vendor_code_column', 10, 2);
function show_vendor_code_column($column, $post_id) {
    if ($column === 'vendor_code') {
        $vendor_code = get_post_meta($post_id, '_vendor_code', true);
        echo esc_html($vendor_code);
    }
}
add_filter('manage_edit-vendor_sortable_columns', 'make_vendor_code_sortable');
function make_vendor_code_sortable($columns) {
    $columns['vendor_code'] = 'vendor_code';
    return $columns;
}

// Handle sorting logic
add_action('pre_get_posts', 'sort_vendor_code_column');
function sort_vendor_code_column($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    if ($query->get('orderby') === 'vendor_code') {
        $query->set('meta_key', '_vendor_code');
        $query->set('orderby', 'meta_value');
    }
}


// Code for adding buy now button in product

// add_action('woocommerce_after_add_to_cart_button', 'custom_buy_now_button_single');

// function custom_buy_now_button_single() {
//     global $product;
//     echo '<a href="' . esc_url($product->add_to_cart_url()) . '&buy_now=true" class="button buy-now-button" style="margin-left:10px;background:#ff6600;color:white;">Buy Now</a>';
// }

// add_action('woocommerce_after_shop_loop_item', 'custom_buy_now_button_archive', 20);

// function custom_buy_now_button_archive() {
//     global $product;
//     echo '<a href="' . esc_url($product->add_to_cart_url()) . '&buy_now=true" class="button buy-now-button" style="margin-left:10px;background:#ff6600;color:white;">Buy Now</a>';
// }

// add_action('template_redirect', 'redirect_buy_now');

// function redirect_buy_now() {
//     if (isset($_GET['buy_now']) && $_GET['buy_now'] === 'true') {
//         // Optional: clear cart before adding new item
//         WC()->cart->empty_cart();

//         // Add the product to cart
//         if (isset($_GET['add-to-cart'])) {
//             $product_id = intval($_GET['add-to-cart']);
//             WC()->cart->add_to_cart($product_id);
//         }

//         // Redirect to checkout
//         wp_safe_redirect(wc_get_checkout_url());
//         exit;
//     }
// }

add_filter( 'woocommerce_loop_add_to_cart_link', 'custom_cart_button_svg', 10, 3 );
function custom_cart_button_svg( $button, $product, $args ) {
    // Check if the product is purchasable and in stock
    if ( $product->is_purchasable() && $product->is_in_stock() ) {
        // Get the dynamic base URL
        $base_url = home_url();
        // Path to the SVG file in the media library
        $svg_path = $base_url . '/wp-content/uploads/2025/05/cart_icon.svg';
        // Load the SVG content
        $button_text = file_get_contents( $svg_path );

        // If SVG file couldn't be loaded, provide a fallback
        if ( $button_text === false ) {
            $button_text = '<span>Cart</span>'; // Fallback text or alternative
        }

        // Construct the button with the SVG
        $button = sprintf(
            '<a href="%s" data-quantity="%s" class="%s" %s>%s</a>',
            esc_url( $product->add_to_cart_url() ),
            esc_attr( isset( $args['quantity'] ) ? $args['quantity'] : 1 ),
            esc_attr( isset( $args['class'] ) ? $args['class'] : 'button' ),
            isset( $args['attributes'] ) ? wc_implode_html_attributes( $args['attributes'] ) : '',
            $button_text
        );
    }
    return $button;
}


add_action( 'woocommerce_checkout_process', 'wc_minimum_order_amount' );
add_action( 'woocommerce_before_cart' , 'wc_minimum_order_amount' );

function wc_minimum_order_amount() {
    // Set this variable to specify a minimum order value
    $minimum = 50;

    if ( WC()->cart->total < $minimum ) {

        if( is_cart() ) {

            wc_print_notice(
                sprintf( 'Your current order total is %s — you must have an order with a minimum of %s to place your order ' ,
                    wc_price( WC()->cart->total ),
                    wc_price( $minimum )
                ), 'error'
            );

        } else {

            wc_add_notice(
                sprintf( 'Your current order total is %s — you must have an order with a minimum of %s to place your order' ,
                    wc_price( WC()->cart->total ),
                    wc_price( $minimum )
                ), 'error'
            );

        }
    }
}

// Variations sork starts from here

// add_filter('wcfm_product_manage_fields_general', function($general_fields, $product_id) {
//     $order_unit = get_post_meta($product_id, '_order_unit', true);
//     $min_order_qty = get_post_meta($product_id, '_min_order_qty', true);

//     $general_fields['order_unit'] = array(
//         'label'       => __('Order Unit', 'woocommerce'),
//         'type'        => 'select',
//         'options'     => array(
//             '' => 'Select',
//             'box' => 'Box',
//             'carton' => 'Carton',
//             'pack' => 'Pack',
//             'tray' => 'Tray'
//         ),
//         'value'       => $order_unit,
//         'desc'        => __('Type of packaging unit (Box, Carton, etc.)'),
//         'class'       => 'wcfm-text wcfm_ele',
//         'label_class' => 'wcfm_title',
//         'hints'       => 'Select the order unit.'
//     );

//     $general_fields['min_order_qty'] = array(
//         'label'       => __('Minimum Order Quantity', 'woocommerce'),
//         'type'        => 'number',
//         'value'       => $min_order_qty,
//         'desc'        => __('Total number of items required per order unit.'),
//         'class'       => 'wcfm-text wcfm_ele',
//         'label_class' => 'wcfm_title',
//         'hints'       => 'Set the minimum quantity that completes a unit.'
//     );

//     return $general_fields;
// }, 50, 2);


// add_action('wcfm_product_manage_fields_save', function($new_product_id, $wcfm_products_manage_form_data) {
//     if (isset($wcfm_products_manage_form_data['order_unit'])) {
//         update_post_meta($new_product_id, '_order_unit', sanitize_text_field($wcfm_products_manage_form_data['order_unit']));
//     }
//     if (isset($wcfm_products_manage_form_data['min_order_qty'])) {
//         update_post_meta($new_product_id, '_min_order_qty', intval($wcfm_products_manage_form_data['min_order_qty']));
//     }
// }, 50, 2);


// add_filter('woocommerce_add_to_cart_validation', 'check_min_order_qty_limit', 10, 3);
// function check_min_order_qty_limit($passed, $product_id, $quantity) {
//     $min_qty = (int) get_post_meta($product_id, '_min_order_qty', true);
//     if ($min_qty && is_array($_REQUEST['quantity'])) {
//         $total = array_sum(array_map('intval', $_REQUEST['quantity']));
//         if ($total < $min_qty) {
//             wc_add_notice('Please select a total quantity of at least ' . $min_qty . '.', 'error');
//             return false;
//         }
//     }
//     return $passed;
// }


add_filter('wcfm_product_custom_fields', function($allowed_fields) {
    $allowed_fields[] = 'order_unit';
    $allowed_fields[] = 'min_order_qty';
    return $allowed_fields;
});

// add_filter('wcfm_product_manage_fields_general', function($general_fields, $product_id) {
//     $general_fields['order_unit'] = [
//         'label' => __('Order Unit', 'woocommerce'),
//         'type'  => 'select',
//         'options' => [
//             '' => 'Select',
//             'box' => 'Box',
//             'carton' => 'Carton',
//             'pack' => 'Pack',
//             'tray' => 'Tray'
//         ],
//         'value' => get_post_meta($product_id, '_order_unit', true),
//         'class' => 'wcfm-select wcfm_ele custom_wcfm_field',
//         'label_class' => 'wcfm_title',
//         'name'  => 'order_unit'
//     ];

//     $general_fields['min_order_qty'] = [
//         'label' => __('Minimum Order Quantity', 'woocommerce'),
//         'type'  => 'number',
//         'value' => get_post_meta($product_id, '_min_order_qty', true),
//         'class' => 'wcfm-text wcfm_ele custom_wcfm_field',
//         'label_class' => 'wcfm_title',
//         'custom_attributes' => ['min' => '1'],
//         'name'  => 'min_order_qty'
//     ];

//     return $general_fields;
// }, 50, 2);

// add_action('after_wcfm_products_manage_meta_save', function($product_id, $form_data) {
//     if (isset($form_data['order_unit'])) {
//         update_post_meta($product_id, '_order_unit', sanitize_text_field($form_data['order_unit']));
//     }

//     if (isset($form_data['min_order_qty'])) {
//         update_post_meta($product_id, '_min_order_qty', intval($form_data['min_order_qty']));
//     }
// }, 100, 2);

function get_custom_product_fields($product_id) {
    return [
        'order_unit' => [
            'label' => __('Order Unit', 'woocommerce'),
            'type'  => 'text',
            'value' => get_post_meta($product_id, '_order_unit', true),
            'class' => 'wcfm-text wcfm_ele custom_wcfm_field',
            'label_class' => 'wcfm_title',
            'name'  => 'order_unit',
            'placeholder' => __('e.g., Box, Carton, Pack, Tray', 'woocommerce')
        ],
        'min_order_qty' => [
            'label' => __('Minimum Order Quantity', 'woocommerce'),
            'type'  => 'number',
            'value' => get_post_meta($product_id, '_min_order_qty', true),
            'class' => 'wcfm-text wcfm_ele custom_wcfm_field',
            'label_class' => 'wcfm_title',
            'custom_attributes' => ['min' => '1'],
            'name'  => 'min_order_qty'
        ]
    ];
}

// Add fields to WCFM for simple products
add_filter('wcfm_product_manage_fields_general', function($general_fields, $product_id) {
    $custom_fields = get_custom_product_fields($product_id);
    return array_merge($general_fields, $custom_fields);
}, 50, 2);

// Add fields to WooCommerce admin for variable products only
add_action('woocommerce_product_options_general_product_data', function() {
    global $post;
    $product = wc_get_product($post->ID);

    // Only show fields for variable products
    if ($product && $product->is_type('variable')) {
        echo '<div class="options_group">';

        woocommerce_wp_text_input([
            'id' => '_order_unit',
            'label' => __('Order Unit', 'woocommerce'),
            'desc_tip' => true,
            'description' => __('The unit in which this product is ordered (e.g., Box, Carton, Pack)', 'woocommerce'),
            'placeholder' => __('e.g., Box, Carton, Pack, Tray', 'woocommerce')
        ]);

        woocommerce_wp_text_input([
            'id' => '_qty_per_order_unit',
            'label' => __('Quantity per Order Unit', 'woocommerce'),
            'type' => 'number',
            'custom_attributes' => ['min' => '1'],
            'desc_tip' => true,
            'description' => __('Number of individual items in one order unit', 'woocommerce')
        ]);

        woocommerce_wp_text_input([
            'id' => '_min_order_qty',
            'label' => __('Minimum Order Quantity', 'woocommerce'),
            'type' => 'number',
//             'custom_attributes' => ['min' => '1'],
            'desc_tip' => true,
            'description' => __('Minimum order quantity for this product', 'woocommerce')
        ]);

        echo '</div>';
    }
});

// Add fields to WooCommerce admin for variable product variations
add_action('woocommerce_product_after_variable_attributes', function($loop, $variation_data, $variation) {
    $product = wc_get_product($variation->post_parent);

    // Confirm parent product is variable
    if ($product && $product->is_type('variable')) {
        ?>
        <div class="variable_custom_field">
            <p class="form-row form-row-full">
                <label><?php _e('Order Unit', 'woocommerce'); ?></label>
                <input type="text" name="variable_order_unit[<?php echo esc_attr($loop); ?>]"
                       value="<?php echo esc_attr(get_post_meta($variation->ID, '_order_unit', true)); ?>"
                       placeholder="<?php _e('e.g., Box, Carton, Pack, Tray', 'woocommerce'); ?>" />
            </p>
            <p class="form-row form-row-full">
                <label><?php _e('Quantity per Order Unit', 'woocommerce'); ?></label>
                <input type="number" min="1" name="variable_qty_per_order_unit[<?php echo esc_attr($loop); ?>]"
                       value="<?php echo esc_attr(get_post_meta($variation->ID, '_qty_per_order_unit', true)); ?>" />
            </p>
            <p class="form-row form-row-full">
                <label><?php _e('Minimum Order Quantity', 'woocommerce'); ?></label>
                <input type="number" min="1" name="variable_min_order_qty[<?php echo esc_attr($loop); ?>]"
                       value="<?php echo esc_attr(get_post_meta($variation->ID, '_min_order_qty', true)); ?>" />
            </p>
        </div>
        <?php
    }
}, 10, 3);

// Save fields for simple and variable products
add_action('woocommerce_process_product_meta', function($product_id) {
    $product = wc_get_product($product_id);

    // Only save for variable products
    if ($product && $product->is_type('variable')) {
        update_post_meta($product_id, '_order_unit', isset($_POST['_order_unit']) ? sanitize_text_field($_POST['_order_unit']) : '');
        update_post_meta($product_id, '_qty_per_order_unit', isset($_POST['_qty_per_order_unit']) ? intval($_POST['_qty_per_order_unit']) : '');
        update_post_meta($product_id, '_min_order_qty', isset($_POST['_min_order_qty']) ? intval($_POST['_min_order_qty']) : '');
    }
});

// Save fields for product variations
add_action('woocommerce_save_product_variation', function($variation_id, $loop) {
    $product = wc_get_product(wp_get_post_parent_id($variation_id));

    // Only save for variations of variable products
    if ($product && $product->is_type('variable')) {
        update_post_meta($variation_id, '_order_unit', isset($_POST['variable_order_unit'][$loop]) ? sanitize_text_field($_POST['variable_order_unit'][$loop]) : '');
        update_post_meta($variation_id, '_qty_per_order_unit', isset($_POST['variable_qty_per_order_unit'][$loop]) ? intval($_POST['variable_qty_per_order_unit'][$loop]) : '');
        update_post_meta($variation_id, '_min_order_qty', isset($_POST['variable_min_order_qty'][$loop]) ? intval($_POST['variable_min_order_qty'][$loop]) : '');
    }
}, 10, 2);

// Save fields for WCFM
add_action('after_wcfm_products_manage_meta_save', function($product_id, $form_data) {
    $product = wc_get_product($product_id);

    // Only save for variable products
    if ($product && $product->is_type('variable')) {
        update_post_meta($product_id, '_order_unit', isset($form_data['order_unit']) ? sanitize_text_field($form_data['order_unit']) : '');
        update_post_meta($product_id, '_qty_per_order_unit', isset($form_data['qty_per_order_unit']) ? intval($form_data['qty_per_order_unit']) : '');
        update_post_meta($product_id, '_min_order_qty', isset($form_data['min_order_qty']) ? intval($form_data['min_order_qty']) : '');
    }
}, 100, 2);

// Display calculated Minimum Order Quantity * Quantity per Order Unit on product detail page
add_action('woocommerce_single_product_summary', function() {
    global $product;

    // Ensure the product is a variable product
    if ($product->is_type('variable')) {
        // Get the necessary metadata
        $min_order_qty = get_post_meta($product->get_id(), '_min_order_qty', true);
        $qty_per_order_unit = get_post_meta($product->get_id(), '_qty_per_order_unit', true);
        $order_unit = get_post_meta($product->get_id(), '_order_unit', true);

        // Calculate the total order quantity
        if ($min_order_qty && $qty_per_order_unit) {
            $calculated_value = $min_order_qty * $qty_per_order_unit;

            // Output the formatted value on the product detail page
            echo '<p><strong>' . __('Minimum Order Quantity:', 'woocommerce') . ' </strong>' . $min_order_qty . ' ' . esc_html($order_unit) . ' = ' . esc_html($calculated_value) . ' ' . __('Pieces', 'woocommerce') . '</p>';
        }
    }
}, 20);  // 20 is the priority, adjust as needed

// Save the calculated Minimum Order Quantity * Quantity per Order Unit
add_action('woocommerce_process_product_meta', function($product_id) {
    $product = wc_get_product($product_id);

    // Only save for variable products
    if ($product && $product->is_type('variable')) {
        $min_order_qty = isset($_POST['_min_order_qty']) ? intval($_POST['_min_order_qty']) : 0;
        $qty_per_order_unit = isset($_POST['_qty_per_order_unit']) ? intval($_POST['_qty_per_order_unit']) : 0;

        // Calculate the value (Minimum Order Quantity * Quantity per Order Unit)
        $calculated_value = $min_order_qty * $qty_per_order_unit;

        // Store the calculated value in a custom field
        update_post_meta($product_id, '_calculated_order_qty', $calculated_value);
    }
});

// Variations work ends here

// for simple product
// Add fields to WooCommerce admin for simple products only
add_action('woocommerce_product_options_general_product_data', function() {
    global $post;
    $product = wc_get_product($post->ID);

    // Only show fields for simple products
    if ($product && $product->is_type('simple')) {
        echo '<div class="options_group">';

        // Add Order Unit field
        woocommerce_wp_text_input([
            'id' => '_order_unit',
            'label' => __('Order Unit', 'woocommerce'),
            'desc_tip' => true,
            'description' => __('The unit in which this product is ordered (e.g., Box, Carton, Pack)', 'woocommerce'),
            'placeholder' => __('e.g., Box, Carton, Pack, Tray', 'woocommerce')
        ]);

        // Add Minimum Order Quantity field
        woocommerce_wp_text_input([
            'id' => '_min_order_qty',
            'label' => __('Minimum Order Quantity', 'woocommerce'),
            'type' => 'number',
            'custom_attributes' => ['min' => '1'],
            'desc_tip' => true,
            'description' => __('The exact quantity customers must order', 'woocommerce')
        ]);

        echo '</div>';
    }
});

// Save fields for simple products in the admin panel
add_action('woocommerce_process_product_meta', function($product_id) {
    $product = wc_get_product($product_id);

    // Only save for simple products
    if ($product && $product->is_type('simple')) {
        update_post_meta($product_id, '_order_unit', isset($_POST['_order_unit']) ? sanitize_text_field($_POST['_order_unit']) : '');
        update_post_meta($product_id, '_min_order_qty', isset($_POST['_min_order_qty']) ? intval($_POST['_min_order_qty']) : '');
    }
});

// Save fields for WCFM Products Management
add_action('after_wcfm_products_manage_meta_save', function($product_id, $form_data) {
    $product = wc_get_product($product_id);

    // Only save for simple products
    if ($product && $product->is_type('simple')) {
        update_post_meta($product_id, '_order_unit', isset($form_data['order_unit']) ? sanitize_text_field($form_data['order_unit']) : '');
        update_post_meta($product_id, '_min_order_qty', isset($form_data['min_order_qty']) ? intval($form_data['min_order_qty']) : '');
    }
}, 100, 2);

// Enforce exact quantity for simple products on the product page
add_filter('woocommerce_quantity_input_args', function($args, $product) {
    if ($product->is_type('simple')) {
        $min_order_qty = get_post_meta($product->get_id(), '_min_order_qty', true);
        if ($min_order_qty) {
            $args['input_value'] = $min_order_qty; // Set default quantity
            $args['min_value'] = $min_order_qty;   // Set minimum quantity
            $args['max_value'] = $min_order_qty;   // Set maximum quantity
        }
    }
    return $args;
}, 10, 2);

// Validate cart quantities for simple products
add_action('woocommerce_check_cart_items', function() {
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        $product = $cart_item['data'];
        if ($product->is_type('simple')) {
            $min_order_qty = get_post_meta($product->get_id(), '_min_order_qty', true);
            // Check if the quantity is less than the minimum order quantity
            if ($min_order_qty && $cart_item['quantity'] < $min_order_qty) {
                wc_add_notice(
                    sprintf(
                        __('The quantity for %s must be at least %d.', 'woocommerce'),
                        $product->get_name(),
                        $min_order_qty
                    ),
                    'error'
                );
            }
        }
    }
});

// Display order unit on product page for simple products
add_action('woocommerce_single_product_summary', function() {
    global $product;
    if ($product->is_type('simple')) {
        $order_unit = get_post_meta($product->get_id(), '_order_unit', true);
        if ($order_unit) {
            echo '<p class="order-unit">' . sprintf(__('Sold in: %s', 'woocommerce'), esc_html($order_unit)) . '</p>';
        }
    }
}, 25);

// end for simple product

// Custom Add to Cart Logic for Bundled Variations
add_action('wp', function() {
    // Ensure cart session is initialized
    if (!WC()->cart) {
        wc_load_cart();
        error_log('Cart session initialized manually');
    }

    if (isset($_POST['add-to-cart']) && isset($_POST['custom_variations_form']) && !empty($_POST['quantity']) && is_array($_POST['quantity'])) {
        $product_id = absint($_POST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product || !$product->is_type('variable')) {
            wc_add_notice(__('Invalid product or product type.', 'woocommerce'), 'error');
            error_log('Invalid product or type: Product ID: ' . $product_id);
            return;
        }

        // Check if product is purchasable
        if (!$product->is_purchasable() || !$product->is_in_stock()) {
            wc_add_notice(__('Product is not purchasable or out of stock.', 'woocommerce'), 'error');
            error_log('Product not purchasable or out of stock: Product ID: ' . $product_id);
            return;
        }

        $min_order_qty = absint(get_post_meta($product_id, '_min_order_qty', true)) ?: 1;
        $total_qty = 0;
        $bundle_data = [];
        $quantity_log = [];
        $bundle_quantity = absint($_POST['bundle_quantity']) ?: 1;

        // Log POST data
        error_log('POST quantity data: ' . print_r($_POST['quantity'], true));

        // Collect quantities and attributes
        foreach ($_POST['quantity'] as $variation_id => $quantity) {
            $quantity = absint($quantity);
            $variation_id = absint($variation_id);

            $quantity_log[$variation_id] = $quantity;

            if ($quantity > 0 && $quantity <= $min_order_qty) {
                $variation_obj = wc_get_product($variation_id);
                if (!$variation_obj) {
                    wc_add_notice(sprintf(__('Invalid variation ID %d.', 'woocommerce'), $variation_id), 'error');
                    error_log('Invalid variation ID: ' . $variation_id);
                    return;
                }
                if ($variation_obj->managing_stock() && $variation_obj->get_stock_quantity() < $quantity) {
                    wc_add_notice(sprintf(__('Insufficient stock for variation %s.', 'woocommerce'), implode(' / ', array_values($_POST['attributes'][$variation_id]))), 'error');
                    error_log('Insufficient stock for variation ID: ' . $variation_id);
                    return;
                }
                $variation_attributes = isset($_POST['attributes'][$variation_id]) ? array_map('sanitize_text_field', $_POST['attributes'][$variation_id]) : [];
                $bundle_data[$variation_id] = [
                    'quantity' => $quantity,
                    'attributes' => $variation_attributes,
                ];
                $total_qty += $quantity;
            }
        }

        // Log quantities
        error_log('Server-side quantities: ' . print_r($quantity_log, true));
        error_log('Server-side total_qty: ' . $total_qty . ', Expected min_order_qty: ' . $min_order_qty);

        // Validate total quantity based on bundle_quantity, not total_qty
        if ($bundle_quantity >= $min_order_qty) {
            // Proceed to add to cart or further actions
            // You can add your cart addition logic here or any other functionality that should happen when the condition passes
        } else {
            wc_add_notice(sprintf(__('Total quantity must be equal to or greater than %d. Current total is %d.', 'woocommerce'), $min_order_qty, $bundle_quantity), 'error');
            error_log('Validation failed: Total quantity ' . $bundle_quantity . ' is less than ' . $min_order_qty);
            return;
        }

        // Validate bundle data
        if (empty($bundle_data)) {
            wc_add_notice(__('No valid variations selected.', 'woocommerce'), 'error');
            error_log('No valid variations in bundle_data');
            return;
        }

// Further actions (e.g., adding products to the cart) can go here


        // Prepare cart item data
        $cart_item_data = [
            'custom_bundle' => $bundle_data,
            'bundle_total_qty' => $total_qty,
        ];

        // Use first variation for cart item
        $first_variation_id = array_key_first($bundle_data);
        $first_variation_attributes = $bundle_data[$first_variation_id]['attributes'];

        // Log before adding to cart
        error_log('Attempting to add bundle to cart: Product ID: ' . $product_id . ', Variation ID: ' . $first_variation_id . ', Bundle Quantity: 1, Variation Total: ' . $total_qty . ', Bundle Data: ' . print_r($bundle_data, true));

        // Add to cart
        $cart_item_key = WC()->cart->add_to_cart(
            $product_id,
            $bundle_quantity, // Bundle quantity
            $first_variation_id,
            $first_variation_attributes,
            $cart_item_data
        );

        if (!$cart_item_key) {
            wc_add_notice(__('Failed to add bundle to the cart.', 'woocommerce'), 'error');
            error_log('Failed to add bundle to cart: Product ID: ' . $product_id . ', Variation ID: ' . $first_variation_id . ', Attributes: ' . print_r($first_variation_attributes, true));
            return;
        }

        // Redirect to cart
        if (!wc_get_notices('error')) {
            error_log('Bundle added successfully, redirecting to cart');
            wp_safe_redirect(wc_get_cart_url());
            exit;
        }
    }
});

// Bypass WooCommerce default variation validation
add_filter('woocommerce_add_to_cart_validation', function($passed, $product_id, $quantity, $variation_id = 0, $variations = []) {
    if (isset($_POST['custom_variations_form'])) {
        $product = wc_get_product($product_id);
        if ($product && $product->is_type('variable')) {
            error_log('Bypassing validation for custom form: Product ID: ' . $product_id . ', Variation ID: ' . $variation_id);
            return true;
        }
    }
    return $passed;
}, 999, 5);

// Save custom bundle data to cart item
add_filter('woocommerce_add_cart_item_data', function($cart_item_data, $product_id, $variation_id) {
    if (isset($_POST['custom_variations_form']) && $_POST['custom_variations_form'] === '1') {
        $custom_bundle = [];

        // Process quantities
        if (isset($_POST['quantity']) && is_array($_POST['quantity'])) {
            foreach ($_POST['quantity'] as $variation_id => $quantities) {
                if (is_array($quantities)) {
                    $custom_bundle[$variation_id] = [];
                    foreach ($quantities as $table_index => $qty) {
                        if (is_numeric($qty) && (int)$qty > 0) {
                            $custom_bundle[$variation_id][$table_index] = (int)$qty;
                        }
                    }
                }
            }
        }

        // Process attributes
        if (isset($_POST['attributes']) && is_array($_POST['attributes'])) {
            foreach ($_POST['attributes'] as $variation_id => $attributes) {
                if (is_array($attributes)) {
                    $cart_item_data['custom_bundle_attributes'][$variation_id] = $attributes;
                }
            }
        }

        // Save custom bundle data to cart item
        if (!empty($custom_bundle)) {
            $cart_item_data['custom_bundle'] = $custom_bundle;
        }

        // Debug: Log the custom bundle data being saved
        error_log('Saving to cart item - Product ID: ' . $product_id . ' | Custom Bundle: ' . print_r($custom_bundle, true));
        error_log('Saving to cart item - Attributes: ' . print_r($cart_item_data['custom_bundle_attributes'] ?? [], true));

        return $cart_item_data;
    }
    return $cart_item_data;
}, 10, 3);

// Display bundled variations in cart
add_filter('woocommerce_get_item_data', function($item_data, $cart_item) {
    if (isset($cart_item['custom_bundle']) && is_array($cart_item['custom_bundle'])) {
        $bundle_details = [];
        $table_counter = 1;

        // Log the raw custom_bundle data for debugging
        error_log('Cart item custom_bundle data: ' . print_r($cart_item['custom_bundle'], true));
        error_log('Cart item attributes data: ' . print_r($cart_item['custom_bundle_attributes'] ?? [], true));

        // Get the product to retrieve order unit
        $product = wc_get_product($cart_item['product_id']);
        $order_unit = $product ? get_post_meta($product->get_id(), '_order_unit', true) : 'Unit';
        $order_unit = $order_unit ?: 'Unit';

        // Group variations by table (based on the structure from your form)
        $tables_data = [];

        // First, organize data by table index
        foreach ($cart_item['custom_bundle'] as $variation_id => $quantities) {
            if (is_array($quantities)) {
                foreach ($quantities as $table_index => $quantity) {
                    if (!isset($tables_data[$table_index])) {
                        $tables_data[$table_index] = [];
                    }
                    if ($quantity > 0) {
                        $tables_data[$table_index][$variation_id] = $quantity;
                    }
                }
            }
        }

        // Sort tables by index
        ksort($tables_data);

        // Process each table
        foreach ($tables_data as $table_index => $variations) {
            $table_label = ucfirst($order_unit) . ' ' . ($table_index + 1);
            $table_variations = [];
            $table_total_qty = 0;

            foreach ($variations as $variation_id => $quantity) {
                $variation = wc_get_product($variation_id);
                if (!$variation) {
                    error_log('Invalid variation ID: ' . $variation_id);
                    continue;
                }

                // Get variation attributes and format them properly
                $attributes = $variation->get_variation_attributes();
                $attribute_names = [];

                if (!empty($attributes)) {
                    foreach ($attributes as $taxonomy => $value) {
                        // Clean up taxonomy name
                        $clean_taxonomy = str_replace('attribute_', '', $taxonomy);
                        $label = wc_attribute_label($clean_taxonomy, $variation);

                        // Handle empty values
                        if (empty($value)) {
                            $value = __('Any', 'woocommerce');
                        } else {
                            // Get the proper term name if it's a taxonomy
                            if (taxonomy_exists($clean_taxonomy)) {
                                $term = get_term_by('slug', $value, $clean_taxonomy);
                                if ($term && !is_wp_error($term)) {
                                    $value = $term->name;
                                }
                            }
                        }

                        $attribute_names[] = $label . ': ' . esc_html($value);
                    }
                    $variation_name = implode(' / ', $attribute_names);
                } else {
                    $variation_name = $variation->get_name();
                }

                if ($quantity > 0) {
                    $table_variations[] = sprintf('%s (Qty: %d)', $variation_name, $quantity);
                    $table_total_qty += $quantity;
                }

                error_log('Table ' . $table_index . ' - Variation ID: ' . $variation_id . ' | Name: ' . $variation_name . ' | Quantity: ' . $quantity);
            }

            // Add table data to bundle details
            if (!empty($table_variations)) {
                $bundle_details[] = [
                    'table_label' => $table_label,
                    'variations' => $table_variations,
                    'total_qty' => $table_total_qty
                ];
            }
        }

        // Format the output for display
        if (!empty($bundle_details)) {
            $formatted_output = '<div class="bundle-contents-wrapper">';

            foreach ($bundle_details as $table_data) {
                $formatted_output .= '<div class="bundle-table-section">';
                $formatted_output .= '<strong class="table-label">' . esc_html($table_data['table_label']) . '</strong>';
                $formatted_output .= '<ul class="variations-list">';

                foreach ($table_data['variations'] as $variation_detail) {
                    $formatted_output .= '<li>' . esc_html($variation_detail) . '</li>';
                }

                $formatted_output .= '</ul>';
                $formatted_output .= '<span class="table-total">Total: ' . $table_data['total_qty'] . '</span>';
                $formatted_output .= '</div>';
            }

            $formatted_output .= '</div>';

            $item_data[] = [
                'key'   => __('Bundle Contents', 'woocommerce'),
                'value' => $formatted_output,
            ];

            // Enhanced JavaScript logging for debugging
            add_action('wp_footer', function() use ($bundle_details, $tables_data) {
                ?>
                <script type="text/javascript">
                    console.log('=== CART DISPLAY DEBUG ===');
                    console.log('Bundle Details (formatted):', <?php echo json_encode($bundle_details); ?>);
                    console.log('Tables Data (raw):', <?php echo json_encode($tables_data); ?>);
                    console.log('=== END CART DISPLAY DEBUG ===');
                </script>
                <?php
            });
        } else {
            error_log('No valid bundle details found for cart item.');
        }
    } else {
        error_log('No custom_bundle data found in cart item.');
    }

    return $item_data;
}, 10, 2);

// Enhanced CSS for better display in cart
add_action('wp_head', function() {
    if (is_cart() || is_checkout()) {
        ?>
        <style type="text/css">
            .bundle-contents-wrapper {
                margin: 10px 0;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                background-color: #f9f9f9;
            }

            .bundle-table-section {
                margin-bottom: 15px;
                padding: 8px;
                background-color: #fff;
                border-radius: 3px;
                border-left: 3px solid #0073aa;
            }

            .bundle-table-section:last-child {
                margin-bottom: 0;
            }

            .table-label {
                display: block;
                font-weight: bold;
                color: #0073aa;
                margin-bottom: 5px;
                font-size: 0.95em;
                text-transform: capitalize;
            }

            .variations-list {
                margin: 5px 0 8px 15px;
                padding: 0;
                list-style-type: disc;
            }

            .variations-list li {
                margin-bottom: 3px;
                font-size: 0.9em;
                color: #555;
            }

            .table-total {
                display: block;
                font-weight: bold;
                font-size: 0.85em;
                color: #666;
                text-align: right;
                border-top: 1px solid #eee;
                padding-top: 5px;
                margin-top: 5px;
            }

            /* Cart table item data styling */
            .woocommerce-cart-form .cart_item .item-data {
                margin-top: 5px;
            }

            .woocommerce-cart-form .cart_item .item-data dt {
                font-weight: bold;
                margin-bottom: 5px;
            }

            .woocommerce-cart-form .cart_item .item-data dd {
                margin-bottom: 10px;
            }

            /* Checkout page styling */
            .woocommerce-checkout .shop_table .cart_item .item-data {
                font-size: 0.9em;
            }

            .woocommerce-checkout .bundle-contents-wrapper {
                font-size: 0.85em;
            }
        </style>
        <?php
    }
});

// Hide Variation Attributes in Cart
add_filter('woocommerce_cart_item_variation', function($variation_data, $cart_item) {
    if (isset($cart_item['custom_bundle'])) {
        return []; // Remove variation attributes to avoid duplicates
    }
    return $variation_data;
}, 10, 2);

// Set Fixed Price for Bundle
add_action('woocommerce_before_calculate_totals', function($cart) {
    if (is_admin() && !defined('DOING_AJAX')) return;
    foreach ($cart->get_cart() as $cart_item) {
        if (isset($cart_item['custom_bundle'])) {
            $product = wc_get_product($cart_item['product_id']);
            $fixed_price = floatval($product->get_price());
            if (!$fixed_price) {
                error_log('No price set for product ID: ' . $cart_item['product_id']);
                $fixed_price = floatval(get_post_meta($cart_item['product_id'], '_fixed_bundle_price', true)) ?: 100; // Fallback price
            }
            $cart_item['data']->set_price($fixed_price);
            error_log('Set fixed price for bundle: Product ID: ' . $cart_item['product_id'] . ', Price: ' . $fixed_price);
        }
    }
});

// Custom Cart Item Name
add_filter('woocommerce_cart_item_name', function($name, $cart_item, $cart_item_key) {
    if (isset($cart_item['custom_bundle'])) {
        return __('Box', 'woocommerce');
    }
    return $name;
}, 10, 3);


// Add MOQ to cart page for simple products
// add_filter('woocommerce_get_item_data', function($item_data, $cart_item) {
//     // Check if the product is simple
//     $product = $cart_item['data'];
//     if ($product->is_type('simple')) {
//         // Get the Minimum Order Quantity for the product
//         $min_order_qty = get_post_meta($product->get_id(), '_min_order_qty', true);
//         if ($min_order_qty) {
//             // Add the MOQ to the item data
//             $item_data[] = [
//                 'key'   => __('Minimum Order Quantity', 'woocommerce'),
//                 'value' => $min_order_qty,
//             ];
//         }
//     }
//     return $item_data;
// }, 10, 2);


// Variations quantity work starts from here
add_shortcode('custom_variations_add_to_cart', function($atts) {
    global $product;
    $atts = shortcode_atts(['product_id' => 0], $atts);
    $product_id = $atts['product_id'] ? absint($atts['product_id']) : ($product ? $product->get_id() : 0);

    if (!$product_id || !($product = wc_get_product($product_id))) {
        return;
    }

    $order_unit = get_post_meta($product_id, '_order_unit', true) ?: 'N/A';

    if ($product->is_type('variable')) {
        $variations = $product->get_available_variations();
        if (empty($variations)) {
            return '<p>' . __('No variations available.', 'woocommerce') . '</p>';
        }

        $min_order_qty = absint(get_post_meta($product_id, '_qty_per_order_unit', true)) ?: 1;
        $min_order_qty2 = absint(get_post_meta($product_id, '_min_order_qty', true)) ?: 1;
        $product_attributes = $product->get_attributes();
        $attribute_names = [];

        foreach ($product_attributes as $attr_key => $attribute) {
            $attr_key_clean = $attribute->is_taxonomy() ? $attr_key : $attribute->get_name();
            $attribute_names[] = esc_html(wc_attribute_label($attr_key_clean, $product));
        }

        $heading = !empty($attribute_names) ? implode(', ', $attribute_names) : __('No Attributes', 'woocommerce');

        ob_start();
        ?>
        <form class="custom-variations-form" method="post" enctype="multipart/form-data" data-product_id="<?php echo esc_attr($product_id); ?>" data-min-order-qty="<?php echo esc_attr($min_order_qty); ?>" data-qty-per-order-unit="<?php echo esc_attr($min_order_qty2); ?>">
            <div class="variations">
                <p class="text-order" style="text-transform: capitalize;padding: 15px 0;font-weight: 800;">How many <?php echo esc_html($order_unit); ?>?</p>
                <h5 style="font-weight: 700;"><?php echo $heading; ?></h5>
                <div class="variations-box">
                    <?php for ($table_index = 0; $table_index < $min_order_qty2; $table_index++) : ?>
                        <div class="variation-wrapper">
                            <table class="variations-table" data-min-order-qty="<?php echo esc_attr($min_order_qty); ?>" data-order-unit="<?php echo esc_attr($order_unit); ?>" data-group-id="group-<?php echo esc_attr($product_id); ?>">
                                <tbody>
                                <?php foreach ($variations as $variation) :
                                    $variation_id = $variation['variation_id'];
                                    $variation_obj = wc_get_product($variation_id);
                                    $attributes = $variation['attributes'];
                                    $variation_name = implode(' / ', array_map('esc_html', array_values($attributes)));
                                    $stock_status = $variation_obj->is_in_stock() ? __('In stock', 'woocommerce') : __('Out of stock', 'woocommerce');
                                    ?>
                                    <tr>
                                        <td><?php echo $variation_name; ?></td>
                                        <td>
                                            <?php if ($variation_obj->is_in_stock()) : ?>
                                                <div class="qty-input">
                                                    <button class="qty-count qty-count--minus" data-action="minus" type="button">-</button>
                                                    <input class="product-qty variation-quantity" type="number" step="1" name="quantity[<?php echo $variation_id; ?>][<?php echo $table_index; ?>]" min="0" max="<?php echo esc_attr($min_order_qty); ?>" value="0" data-variation-id="<?php echo esc_attr($variation_id); ?>" data-max-qty="<?php echo esc_attr($min_order_qty); ?>" data-variation-name="<?php echo esc_attr($variation_name); ?>" readonly>
                                                    <button class="qty-count qty-count--add" data-action="add" type="button">+</button>
                                                </div>
                                                <?php foreach ($attributes as $attr_key => $attr_value) : ?>
                                                    <input type="hidden" name="attributes[<?php echo $variation_id; ?>][<?php echo $table_index; ?>][<?php echo esc_attr($attr_key); ?>]" value="<?php echo esc_attr($attr_value); ?>" />
                                                <?php endforeach; ?>
                                            <?php else : ?>
                                                <input type="number" readonly value="0" />
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                            <?php if ($table_index > 0) : ?>
                                <label class="same-as-above" style="margin-top: 10px; display: block;">
                                    <input type="checkbox" class="same-as-above-checkbox"> Same as above
                                </label>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
            <input type="hidden" name="add-to-cart" value="<?php echo esc_attr($product_id); ?>" />
            <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>" />
            <input type="hidden" name="custom_variations_form" value="1" />
            <div class="bundle_quantity_add_to_cart_wrapper" style="display: flex; align-items: center; gap: 10px; margin-top: 10px;">
                <div class="qty-input">
                    <button class="qty-count qty-count--minus" data-action="minus" type="button" <?php echo ($min_order_qty2 === 1) ? 'disabled' : ''; ?>>-</button>
                    <input class="product-qty bundle-quantity" type="number" step="1" name="bundle_quantity" min="<?php echo esc_attr($min_order_qty2); ?>" value="<?php echo esc_attr($min_order_qty2); ?>" data-max-qty="9999" readonly>
                    <button class="qty-count qty-count--add" data-action="add" type="button">+</button>
                </div>
                <button type="submit" class="single_add_to_cart_button button alt"><?php echo esc_html($product->single_add_to_cart_text()); ?></button>
            </div>
        </form>
        <?php
    } elseif ($product->is_type('simple')) {
        ob_start();
        ?>
        <form class="custom-simple-form" method="post" enctype="multipart/form-data" data-product_id="<?php echo esc_attr($product_id); ?>">
            <div class="simple-product">
                <div class="qty-input">
                    <button class="qty-count qty-count--minus" data-action="minus" type="button">-</button>
                    <input class="product-qty" type="number" step="1" name="quantity" min="1" value="1" readonly />
                    <button class="qty-count qty-count--add" data-action="add" type="button">+</button>
                </div>
                <button type="submit" class="single_add_to_cart_button button alt"><?php echo esc_html($product->single_add_to_cart_text()); ?></button>
                <input type="hidden" name="add-to-cart" value="<?php echo esc_attr($product_id); ?>" />
            </div>
        </form>
        <?php
    }

    return ob_get_clean();
});

add_action('wp_enqueue_scripts', function() {
    if (is_product()) {
        wp_enqueue_script('jquery');
        wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', [], null, true);
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                // Debug: Verify jQuery is loaded
                console.log("jQuery loaded:", typeof $ === "function");

                // Function to console log all selected variations by table
                function logSelectedVariations() {
                    console.log("=== SELECTED VARIATIONS BY TABLE ===");

                    $(".variations-table").each(function(tableIndex) {
                        var $table = $(this);
                        var orderUnit = $table.data("order-unit") || "Unit";
                        var tableLabel = orderUnit + " " + (tableIndex + 1);
                        var selectedVariations = [];
                        var totalQty = 0;

                        $table.find(".variation-quantity").each(function() {
                            var $input = $(this);
                            var qty = parseInt($input.val()) || 0;
                            var variationId = $input.data("variation-id");
                            var variationName = $input.data("variation-name") || "Unknown";

                            if (qty > 0) {
                                selectedVariations.push({
                                    variationId: variationId,
                                    name: variationName,
                                    quantity: qty
                                });
                                totalQty += qty;
                            }
                        });

                        console.log("Table: " + tableLabel);
                        console.log("Selected Variations:", selectedVariations);
                        console.log("Total Quantity:", totalQty);
                        console.log("---");
                    });

                    console.log("=== END SELECTED VARIATIONS ===");
                }

                function validateTable($table, tableIndex) {
                    var minOrderQty = parseInt($table.data("min-order-qty")) || 0;
                    if (!minOrderQty) {
                        Swal.fire({
                            icon: "error",
                            title: "Configuration Error",
                            text: "Table " + (tableIndex + 1) + " is missing a valid min-order-qty.",
                            confirmButtonColor: "#0073aa"
                        });
                        return false;
                    }

                    var totalQty = 0;
                    var quantities = [];
                    var valid = true;

                    $table.find(".variation-quantity").each(function() {
                        var $input = $(this);
                        var variationId = $input.data("variation-id");
                        var flavor = $input.closest("tr").find("td:first").text().trim();
                        var maxQty = parseInt($input.data("max-qty")) || minOrderQty;
                        var qty = parseInt($input.val()) || 0;

                        quantities.push({
                            variationId: variationId,
                            flavor: flavor,
                            quantity: qty,
                            maxQty: maxQty
                        });

                        if (qty > maxQty) {
                            valid = false;
                            Swal.fire({
                                icon: "error",
                                title: "Quantity Exceeded in " + flavor,
                                text: "Quantity for " + flavor + " (ID: " + variationId + ") cannot exceed " + maxQty + ".",
                                confirmButtonColor: "#0073aa"
                            });
                        }

                        totalQty += qty;
                    });

                    console.log("Table " + (tableIndex + 1) + " quantities:", quantities);
                    console.log("Table " + (tableIndex + 1) + " totalQty:", totalQty, "Expected minOrderQty:", minOrderQty);

                    if (totalQty !== minOrderQty) {
                        valid = false;
                        Swal.fire({
                            icon: "error",
                            title: "Invalid Total Quantity in " + $table.data("order-unit"),
                            text: "The total quantity for " + $table.data("order-unit") + " " + (tableIndex + 1) + " must be exactly " + minOrderQty + ". Current total is " + totalQty + ".",
                            confirmButtonColor: "#0073aa"
                        });
                    }

                    return valid;
                }

                function updateVariationLabels() {
                    $(".variations .variations-table").each(function(index) {
                        var variationLabel = "Variation " + (index + 1);
                        var orderUnit = $(this).data("order-unit");
                        if (orderUnit) {
                            variationLabel = orderUnit + " " + (index + 1);
                        }
                        if (!$(this).find(".variation-label").length) {
                            $(this).prepend("<div class=\"variation-label\" style=\"text-transform:capitalize; font-weight: bold; margin-bottom: 10px;\">" + variationLabel + "</div>");
                        } else {
                            $(this).find(".variation-label").text(variationLabel);
                        }
                    });
                }

                // Handle "Same as above" checkbox
                $(".variations").on("change", ".same-as-above-checkbox", function() {
                    var $checkbox = $(this);
                    var $currentWrapper = $checkbox.closest(".variation-wrapper");
                    var $currentTable = $currentWrapper.find(".variations-table");
                    var currentIndex = $(".variations .variations-table").index($currentTable);
                    if (currentIndex <= 0) return; // No previous table for first table

                    var $prevTable = $(".variations .variations-table").eq(currentIndex - 1);

                    if ($checkbox.is(":checked")) {
                        $prevTable.find(".variation-quantity").each(function() {
                            var $prevInput = $(this);
                            var variationId = $prevInput.data("variation-id");
                            var qty = parseInt($prevInput.val()) || 0;
                            $currentTable.find(".variation-quantity[data-variation-id=\'" + variationId + "\']").val(qty).trigger("change");
                        });
                    } else {
                        $currentTable.find(".variation-quantity").val(0).trigger("change");
                    }

                    // Log variations after same as above action
                    logSelectedVariations();
                });

                updateVariationLabels();

                $(".bundle_quantity_add_to_cart_wrapper .qty-count--add").on("click", function(e) {
                    e.preventDefault();
                    var $productinput = $(this).siblings(".product-qty");
                    var $productval = parseInt($productinput.val()) || 0;

                    var clonedTable = $(".variations .variations-table:first").clone(true);
                    clonedTable.find("input, select").val("");
                    clonedTable.find(".variation-quantity").val(0);

                    var minOrderQty = parseInt($(".variations .variations-table:first").data("min-order-qty")) || 0;
                    clonedTable.attr("data-min-order-qty", minOrderQty);

                    var groupId = $(".variations .variations-table:first").data("group-id") || "group-" + Date.now();
                    clonedTable.attr("data-group-id", groupId);

                    var tableIndex = $(".variations .variations-table").length;
                    clonedTable.find(".variation-quantity").each(function() {
                        var $input = $(this);
                        var variationId = $input.data("variation-id");
                        $input.attr("name", "quantity[" + variationId + "][" + tableIndex + "]");
                    });
                    clonedTable.find("input[name^=\"attributes\"]").each(function() {
                        var $input = $(this);
                        var variationId = $input.attr("name").match(/\[(\d+)\]/)[1];
                        var attrName = $input.attr("name").match(/\[([^\]]+)\]$/)[1];
                        $input.attr("name", "attributes[" + variationId + "][" + tableIndex + "][" + attrName + "]");
                    });

                    // Debug: Verify $wrapper creation
                    var $wrapper = $("<div class=\"variation-wrapper\"></div>");
                    console.log("Wrapper created for new table:", $wrapper);

                    // Add table and "Same as above" checkbox below it
                    $wrapper.append(clonedTable);
                    $wrapper.append(\'<label class="same-as-above" style="margin-top: 10px; display: block;"><input type="checkbox" class="same-as-above-checkbox"> Same as above</label>\');
                    $(".variations .variations-box").append($wrapper);
                    $productinput.val($productval + 1).trigger("change");
                    updateVariationLabels();

                    // Log variations after adding new table
                    logSelectedVariations();
                });

                $(".bundle_quantity_add_to_cart_wrapper .qty-count--minus").on("click", function(e) {
                    e.preventDefault();
                    var $productinput = $(this).siblings(".product-qty");
                    var $productval = parseInt($productinput.val()) || 0;
                    var minQty = parseInt($productinput.attr("min")) || 1;

                    if ($productval > minQty && $(".variations .variations-table").length > minQty) {
                        $(".variations .variation-wrapper:last").remove();
                        $productinput.val($productval - 1).trigger("change");
                        updateVariationLabels();

                        // Log variations after removing table
                        logSelectedVariations();
                    }
                });

                $(".variations").on("click", ".qty-count--add", function() {
                    var $input = $(this).siblings(".variation-quantity");
                    var $table = $(this).closest(".variations-table");
                    var val = parseInt($input.val()) || 0;
                    var max = parseInt($table.data("min-order-qty")) || 1;
                    var orderUnit = $table.data("order-unit");
                    var totalQty = 0;
                    $table.find(".variation-quantity").each(function() {
                        var qty = parseInt($(this).val()) || 0;
                        if ($(this).is($input)) {
                            totalQty += qty + 1;
                        } else {
                            totalQty += qty;
                        }
                    });
                    var tableIndex = $table.closest(".variations").find(".variations-table").index($table) + 1;

                    if (totalQty <= max) {
                        $input.val(val + 1).trigger("change");

                        // Log variations after quantity change
                        logSelectedVariations();
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Cannot Increment",
                            text: "Total quantity in " + orderUnit + " " + tableIndex + " would exceed " + max + ".",
                            confirmButtonColor: "#0073aa"
                        });
                    }
                });

                $(".variations").on("click", ".qty-count--minus", function() {
                    var $input = $(this).siblings(".variation-quantity");
                    var val = parseInt($input.val()) || 0;
                    var min = parseInt($input.attr("min")) || 0;

                    if (val > min) {
                        $input.val(val - 1).trigger("change");

                        // Log variations after quantity change
                        logSelectedVariations();
                    }
                });

                $(".variations").on("input change", ".variation-quantity", function() {
                    var $input = $(this);
                    var $table = $input.closest(".variations-table");
                    var val = parseInt($input.val()) || 0;
                    var max = parseInt($table.data("min-order-qty")) || 1;
                    var orderUnit = $table.data("order-unit");
                    var totalQty = 0;

                    $table.find(".variation-quantity").each(function() {
                        var qty = parseInt($(this).val()) || 0;
                        if ($(this).is($input)) {
                            totalQty += val;
                        } else {
                            totalQty += qty;
                        }
                    });
                    var tableIndex = $table.closest(".variations").find(".variations-table").index($table) + 1;

                    if (totalQty > max) {
                        Swal.fire({
                            icon: "error",
                            title: "Cannot Increment",
                            text: "Total quantity in " + orderUnit + " " + tableIndex + " would exceed " + max + ".",
                            confirmButtonColor: "#0073aa"
                        });
                        $input.val(Math.max(0, val - (totalQty - max)));
                    }
                });

                $(".custom-simple-form .qty-count").on("click", function() {
                    var $input = $(this).siblings(".product-qty");
                    var currentValue = parseInt($input.val()) || 0;
                    var action = $(this).data("action");
                    var maxQty = $input.attr("max") ? parseInt($input.attr("max")) : Infinity;
                    var minQty = $input.attr("min") ? parseInt($input.attr("min")) : 1;

                    if (action === "add" && currentValue < maxQty) {
                        $input.val(currentValue + 1).trigger("change");
                    } else if (action === "minus" && currentValue > minQty) {
                        $input.val(currentValue - 1).trigger("change");
                    }
                });

                $(".custom-variations-form").on("submit", function(e) {
                    var $form = $(this);
                    var valid = true;
                    var minOrderQty = parseInt($form.data("qty-per-order-unit")) || 0;
                    var bundleQuantity = parseInt($form.find(".bundle-quantity").val()) || 0;
                    var tableCount = $form.find(".variations-table").length;

                    // Log all selected variations before submission
                    console.log("=== FORM SUBMISSION - FINAL SELECTED VARIATIONS ===");
                    logSelectedVariations();

                    if (tableCount !== bundleQuantity) {
                        valid = false;
                        Swal.fire({
                            icon: "error",
                            title: "Invalid Number of Variations",
                            text: "The number of variation tables (" + tableCount + ") must match the bundle quantity (" + bundleQuantity + ").",
                            confirmButtonColor: "#0073aa"
                        });
                    }

                    var groups = {};
                    $form.find(".variations-table").each(function(index) {
                        var $table = $(this);
                        var groupId = $table.data("group-id") || "default";
                        if (!groups[groupId]) {
                            groups[groupId] = [];
                        }
                        groups[groupId].push({ table: $table, index: index });
                    });

                    $.each(groups, function(groupId, tables) {
                        tables.forEach(function(tableObj) {
                            if (!validateTable(tableObj.table, tableObj.index)) {
                                valid = false;
                            }
                        });
                    });

                    if (bundleQuantity < minOrderQty) {
                        valid = false;
                        Swal.fire({
                            icon: "error",
                            title: "Invalid Bundle Quantity",
                            text: "The bundle quantity must be at least " + minOrderQty + ". Current quantity is " + bundleQuantity + ".",
                            confirmButtonColor: "#0073aa"
                        });
                    }

                    if (!valid) {
                        e.preventDefault();
                        return false;
                    }
                });

                // Initial log on page load
                logSelectedVariations();
            });
        ');
    }
});
//end script
function custom_product_quantity_validation() {
    if (is_product()) { // Only apply on single product pages
        global $product;

        // Get the _min_order_qty meta field for the product
        $min_order_qty = get_post_meta($product->get_id(), '_min_order_qty', true) ?: 1;

        // Add SweetAlert and validation JavaScript
        ?>
        <script type="text/javascript">
            document.addEventListener('DOMContentLoaded', function() {
                // Add event listener to the simple product form submission
                document.querySelector('.custom-simple-form').addEventListener('submit', function(e) {
                    var form = e.target;
                    var quantityInput = form.querySelector('.product-qty');
                    var quantityValue = parseInt(quantityInput.value);
                    var minOrderQty = <?php echo esc_js($min_order_qty); ?>; // Use the min_order_qty value from PHP

                    // Check if the quantity is less than the minimum required
                    if (quantityValue < minOrderQty) {
                        // If the quantity is less than min_order_qty, prevent form submission and show SweetAlert
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid Quantity',
                            text: 'The quantity must be at least ' + minOrderQty + '. Current quantity is ' + quantityValue + '.',
                            confirmButtonColor: "#0073aa"
                        });

                        e.preventDefault(); // Prevent form submission
                        return false; // Stop further actions
                    }

                    // If the validation is passed, allow form submission
                    console.log("Form submission valid, proceeding...");
                });
            });
        </script>
        <?php
    }
}

add_action('wp_footer', 'custom_product_quantity_validation');


add_action('woocommerce_product_options_general_product_data', 'add_variable_product_price_fields');
function add_variable_product_price_fields() {
    global $woocommerce, $post;
    $product = wc_get_product($post->ID);

    // Only show fields for variable products
    if ($product && $product->is_type('variable')) {
        $display_style = 'display:block;';
    } else {
        $display_style = 'display:none;';
    }

    echo '<div class="options_group variable_pricing" style="' . esc_attr($display_style) . '">';

    // Regular Price Field
    woocommerce_wp_text_input(array(
        'id' => '_variable_regular_price',
        'label' => __('Regular Price for All Variations ($)', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Set a regular price that will apply to all variations.', 'woocommerce'),
        'value' => get_post_meta($post->ID, '_variable_regular_price', true),
        'data_type' => 'price',
    ));

    // Sale Price Field
    woocommerce_wp_text_input(array(
        'id' => '_variable_sale_price',
        'label' => __('Sale Price for All Variations ($)', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Set a sale price that will apply to all variations.', 'woocommerce'),
        'value' => get_post_meta($post->ID, '_variable_sale_price', true),
        'data_type' => 'price',
    ));

    // Cost Field
    woocommerce_wp_text_input(array(
        'id' => '_variable_cost',
        'label' => __('Cafe Collect Cost for All Variations ($)', 'woocommerce'),
        'desc_tip' => 'true',
        'description' => __('Set a cost price that will apply to all variations.', 'woocommerce'),
        'value' => get_post_meta($post->ID, '_variable_cost', true),
        'data_type' => 'price',
    ));

    echo '</div>';

    // Add JavaScript to handle product type change
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Function to toggle visibility of variable pricing fields
            function toggleVariablePricingFields() {
                var productType = $('#product-type').val();
                if (productType === 'variable') {
                    $('.variable_pricing').show();
                } else {
                    $('.variable_pricing').hide();
                }
            }

            // Run on page load
            toggleVariablePricingFields();

            // Run on product type change
            $('#product-type').on('change', function() {
                toggleVariablePricingFields();
            });
        });
    </script>
    <?php
}
// Add custom "Cafe Collect Cost" field for simple products only in WooCommerce admin
add_action('woocommerce_product_options_general_product_data', 'add_cafe_collect_cost_field');
function add_cafe_collect_cost_field() {
    global $post;

    // Get the product type
    $product = wc_get_product($post->ID);

    // Only show the field for simple products
    if ($product && $product->is_type('simple')) {
        // Adding the "Cafe Collect Cost" field
        woocommerce_wp_text_input(array(
            'id' => '_cafe_collect_cost',
            'label' => __('Cafe Collect Cost ($)', 'woocommerce'),
            'desc_tip' => 'true',
            'description' => __('Enter the Cafe Collect Cost for this product.', 'woocommerce'),
            'value' => get_post_meta($post->ID, '_cafe_collect_cost', true),
            'data_type' => 'price',
        ));
    }
}

// Save the "Cafe Collect Cost" field value
add_action('woocommerce_process_product_meta', 'save_cafe_collect_cost_field');
function save_cafe_collect_cost_field($post_id) {
    // Check if the custom field is set and save its value
    if (isset($_POST['_cafe_collect_cost'])) {
        update_post_meta($post_id, '_cafe_collect_cost', sanitize_text_field($_POST['_cafe_collect_cost']));
    }
}


// Save the custom price fields and apply to all variations
add_action('woocommerce_process_product_meta', 'save_variable_product_price_fields');
function save_variable_product_price_fields($post_id) {
    $product = wc_get_product($post_id);

    if ($product && $product->is_type('variable')) {
        // Save regular price
        $variable_regular_price = isset($_POST['_variable_regular_price']) ? wc_format_decimal($_POST['_variable_regular_price']) : '';
        update_post_meta($post_id, '_variable_regular_price', $variable_regular_price);

        // Save sale price
        $variable_sale_price = isset($_POST['_variable_sale_price']) ? wc_format_decimal($_POST['_variable_sale_price']) : '';
        update_post_meta($post_id, '_variable_sale_price', $variable_sale_price);

        // Apply prices to all variations
        $variations = $product->get_available_variations();
        foreach ($variations as $variation) {
            $variation_id = $variation['variation_id'];
            if ($variable_regular_price !== '') {
                update_post_meta($variation_id, '_regular_price', $variable_regular_price);
                update_post_meta($variation_id, '_price', $variable_regular_price);
            }
            if ($variable_sale_price !== '' && $variable_sale_price < $variable_regular_price) {
                update_post_meta($variation_id, '_sale_price', $variable_sale_price);
                update_post_meta($variation_id, '_price', $variable_sale_price); // Update active price to sale price
            } elseif ($variable_sale_price === '' || $variable_sale_price >= $variable_regular_price) {
                delete_post_meta($variation_id, '_sale_price');
                if ($variable_regular_price !== '') {
                    update_post_meta($variation_id, '_price', $variable_regular_price);
                }
            }
        }
    }
}

// Add custom price fields to WCFM vendor dashboard
add_action('wcfm_product_manage_fields_pricing', 'add_wcfm_variable_product_price_fields', 10, 3);
function add_wcfm_variable_product_price_fields($pricing_fields, $product_id, $product_type) {
    if ($product_type === 'variable') {
        $pricing_fields['_variable_regular_price'] = array(
            'label' => __('Regular Price for All Variations', 'woocommerce'),
            'type' => 'number',
            'value' => get_post_meta($product_id, '_variable_regular_price', true),
            'class' => 'wcfm-text wcfm_ele variable',
            'label_class' => 'wcfm_title variable',
            'desc' => __('Set a regular price that will apply to all variations.', 'woocommerce'),
            'desc_class' => 'wcfm_ele variable'
        );

        $pricing_fields['_variable_sale_price'] = array(
            'label' => __('Sale Price for All Variations', 'woocommerce'),
            'type' => 'number',
            'value' => get_post_meta($product_id, '_variable_sale_price', true),
            'class' => 'wcfm-text wcfm_ele variable',
            'label_class' => 'wcfm_title variable',
            'desc' => __('Set a sale price that will apply to all variations.', 'woocommerce'),
            'desc_class' => 'wcfm_ele variable'
        );
    }
    return $pricing_fields;
}

// Save WCFM custom price fields
add_action('after_wcfm_products_manage_meta_save', 'save_wcfm_variable_product_price_fields', 10, 2);
function save_wcfm_variable_product_price_fields($product_id, $wcfm_products_manage_form_data) {
    $product = wc_get_product($product_id);

    if ($product && $product->is_type('variable')) {
        // Save regular price
        $variable_regular_price = isset($wcfm_products_manage_form_data['_variable_regular_price']) ? wc_format_decimal($wcfm_products_manage_form_data['_variable_regular_price']) : '';
        update_post_meta($product_id, '_variable_regular_price', $variable_regular_price);

        // Save sale price
        $variable_sale_price = isset($wcfm_products_manage_form_data['_variable_sale_price']) ? wc_format_decimal($wcfm_products_manage_form_data['_variable_sale_price']) : '';
        update_post_meta($product_id, '_variable_sale_price', $variable_sale_price);

        // Apply prices to all variations
        $variations = $product->get_available_variations();
        foreach ($variations as $variation) {
            $variation_id = $variation['variation_id'];
            if ($variable_regular_price !== '') {
                update_post_meta($variation_id, '_regular_price', $variable_regular_price);
                update_post_meta($variation_id, '_price', $variable_regular_price);
            }
            if ($variable_sale_price !== '' && $variable_sale_price < $variable_regular_price) {
                update_post_meta($variation_id, '_sale_price', $variable_sale_price);
                update_post_meta($variation_id, '_price', $variable_sale_price);
            } elseif ($variable_sale_price === '' || $variable_sale_price >= $variable_regular_price) {
                delete_post_meta($variation_id, '_sale_price');
                if ($variable_regular_price !== '') {
                    update_post_meta($variation_id, '_price', $variable_regular_price);
                }
            }
        }
    }
}

// Customize price display on frontend for variable products
add_filter('woocommerce_variable_price_html', 'custom_variable_price_display', 10, 2);
function custom_variable_price_display($price, $product) {
    if ($product->is_type('variable')) {
        $regular_price = get_post_meta($product->get_id(), '_variable_regular_price', true);
        $sale_price = get_post_meta($product->get_id(), '_variable_sale_price', true);

        if ($regular_price !== '' && $sale_price !== '' && $sale_price < $regular_price) {
            $price = '<del>' . wc_price($regular_price) . '</del> <ins>' . wc_price($sale_price) . '</ins>';
        } elseif ($regular_price !== '') {
            $price = wc_price($regular_price);
        }
    }
    return $price;
}

// Ensure variation price updates dynamically on selection
add_action('wp_footer', 'add_variable_price_update_script');
function add_variable_price_update_script() {
    if (is_product()) {
        global $product;
        if ($product && $product->is_type('variable')) {
            ?>
            <script>
                jQuery(document).ready(function($) {
                    $('form.variations_form').on('show_variation', function(event, variation) {
                        var regular_price = '<?php echo esc_js(get_post_meta($product->get_id(), '_variable_regular_price', true)); ?>';
                        var sale_price = '<?php echo esc_js(get_post_meta($product->get_id(), '_variable_sale_price', true)); ?>';
                        var price_html = '';
                        if (regular_price && sale_price && parseFloat(sale_price) < parseFloat(regular_price)) {
                            price_html = '<del>' + wc_price(regular_price) + '</del> <ins>' + wc_price(sale_price) + '</ins>';
                        } else if (regular_price) {
                            price_html = wc_price(regular_price);
                        }
                        if (price_html) {
                            $('.woocommerce div.product .summary .price').html(price_html);
                        }
                    });

                    // Helper function to format price (mimics wc_price)
                    function wc_price(price) {
                        var currency_symbol = '<?php echo esc_js(get_woocommerce_currency_symbol()); ?>';
                        return currency_symbol + parseFloat(price).toFixed(2);
                    }
                });
            </script>
            <?php
        }
    }
}


// NEW FLOW STARTS FROM HERE


/**
 * Register Vendor Custom Post Type with Custom Fields
 */
function vendor_custom_post_type() {
    // Set UI labels
    $labels = array(
        'name'                => _x('Vendors', 'Post Type General Name', 'hello-elementor'),
        'singular_name'       => _x('Vendor', 'Post Type Singular Name', 'hello-elementor'),
        'menu_name'           => __('Vendors', 'hello-elementor'),
        'all_items'           => __('All Vendors', 'hello-elementor'),
        'view_item'           => __('View Vendor', 'hello-elementor'),
        'add_new_item'        => __('Add New Vendor', 'hello-elementor'),
        'add_new'            => __('Add New', 'hello-elementor'),
        'edit_item'           => __('Edit Vendor', 'hello-elementor'),
        'update_item'         => __('Update Vendor', 'hello-elementor'),
        'search_items'        => __('Search Vendors', 'hello-elementor'),
        'not_found'           => __('No Vendors Found', 'hello-elementor'),
        'not_found_in_trash' => __('No Vendors Found in Trash', 'hello-elementor')
    );

    // CPT options
    $args = array(
        'label'               => __('vendors', 'hello-elementor'),
        'description'         => __('Coffee vendor management', 'hello-elementor'),
        'labels'              => $labels,
        'supports'           => array('title', 'thumbnail'),
        'hierarchical'       => false,
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'menu_icon'          => 'dashicons-store',
        'show_in_nav_menus'  => true,
        'show_in_admin_bar'  => true,
        'menu_position'      => 56,
        'can_export'         => true,
        'has_archive'        => true,
        'exclude_from_search'=> false,
        'publicly_queryable'=> true,
        'capability_type'    => 'post',
        'show_in_rest'      => true,
    );
    register_post_type('vendor', $args);
}
add_action('init', 'vendor_custom_post_type', 0);

/**
 * Add Vendor Meta Boxes
 */
function add_vendor_meta_boxes() {
    add_meta_box(
        'vendor_details',
        'Vendor Information',
        'render_vendor_meta_box',
        'vendor',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_vendor_meta_boxes');

/**
 * Render Vendor Meta Box
 */
function render_vendor_meta_box($post) {
    wp_nonce_field('vendor_meta_box_nonce', 'vendor_meta_box_nonce');

    // Get existing values
    $individual_name = get_post_meta($post->ID, '_individual_name', true);
    $business_name = get_post_meta($post->ID, '_business_name', true);
    $email = get_post_meta($post->ID, '_email', true);
    $resale_license = get_post_meta($post->ID, '_resale_license', true);
    $vendor_code = get_post_meta($post->ID, '_vendor_code', true);
    $vendor_code = ($vendor_code !== '') ? esc_attr($vendor_code) : 'CC';
    $product_description = get_post_meta($post->ID, '_product_description', true);
    $supplier_categories = get_post_meta($post->ID, '_supplier_categories', true) ?: array();

    // Supplier categories options
    $categories = array(
        'Nutrition Bars & Eats',
        'Chocolate & Sweets',
        'Barista Equipment',
        'Cups & Lids',
        'Syrups & Mixers',
        'Dairy/Non-Dairy',
        'Tea & Chai',
        'Coffee Beans',
        'Energy Drinks',
        'Baked Goods',
        'Display & Promotional Material',
        'Others'
    );

    // Form fields
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">';

    // Left column
    echo '<div>';
    echo '<p><label for="individual_name" style="display: block; margin-bottom: 5px; font-weight: bold;">Individual Name *</label>';
    echo '<input type="text" id="individual_name" name="individual_name" value="'.esc_attr($individual_name).'" style="width: 100%;" required /></p>';

    echo '<p><label for="business_name" style="display: block; margin-bottom: 5px; font-weight: bold;">Business Name *</label>';
    echo '<input type="text" id="business_name" name="business_name" value="'.esc_attr($business_name).'" style="width: 100%;" required /></p>';

    echo '<p><label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email *</label>';
    echo '<input type="email" id="email" name="email" value="'.esc_attr($email).'" style="width: 100%;" required /></p>';

    echo '<p><label for="resale_license" style="display: block; margin-bottom: 5px; font-weight: bold;">Resale License Number</label>';
    echo '<input type="text" id="resale_license" name="resale_license" value="'.esc_attr($resale_license).'" style="width: 100%;" /></p>';
    echo '<p><label for="vendor_code" style="display: block; margin-bottom: 5px; font-weight: bold;">Vendor Code</label>';
    echo '<input type="text" id="vendor_code" name="vendor_code" value="'.$vendor_code.'" style="width: 100%;" /></p>';


    echo '</div>';

    // Right column
    echo '<div>';
    echo '<p style="margin-bottom: 5px; font-weight: bold;">Supplier Details</p>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">';

    foreach ($categories as $category) {
        $checked = in_array($category, $supplier_categories) ? 'checked' : '';
        echo '<label style="display: flex; align-items: center; gap: 5px;">';
        echo '<input type="checkbox" name="supplier_categories[]" value="'.esc_attr($category).'" '.$checked.'>';
        echo esc_html($category);
        echo '</label>';
    }

    echo '</div>';
    echo '</div>';

    echo '</div>'; // Close grid

    echo '<p><label for="product_description" style="display: block; margin: 20px 0 5px; font-weight: bold;">Describe Your Products *</label>';
    wp_editor($product_description, 'product_description', array(
        'textarea_name' => 'product_description',
        'media_buttons' => false,
        'textarea_rows' => 5,
        'teeny' => true
    ));
    echo '</p>';
}

/**
 * Save Vendor Meta Data
 */
function save_vendor_meta($post_id) {
    // Verify nonce
    if (!isset($_POST['vendor_meta_box_nonce']) || !wp_verify_nonce($_POST['vendor_meta_box_nonce'], 'vendor_meta_box_nonce')) {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save fields
    $fields = array(
        'individual_name',
        'business_name',
        'email',
        'resale_license',
        'product_description'
    );
    // Save vendor code
    // Auto-generate vendor code if it is exactly '1c2'
    if (isset($_POST['vendor_code'])) {
        $input_code = trim($_POST['vendor_code']);

        if ($input_code === 'CC') {
            // Count existing vendor_codes like '1c2-%'
            $existing_vendors = new WP_Query([
                'post_type'      => 'vendor',
                'post_status'    => ['publish', 'draft', 'pending', 'future'],
                'posts_per_page' => -1,
                'meta_query'     => [
                    [
                        'key'     => '_vendor_code',
                        'value'   => 'CC-',
                        'compare' => 'LIKE'
                    ]
                ],
                'fields' => 'ids'
            ]);

            $count = count($existing_vendors->posts);
            $new_vendor_code = 'CC-' . ($count + 1);
            update_post_meta($post_id, '_vendor_code', $new_vendor_code);
        } else {
            update_post_meta($post_id, '_vendor_code', sanitize_text_field($input_code));
        }
    }



    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_'.$field, sanitize_text_field($_POST[$field]));
        }
    }

    // Save categories
    $categories = isset($_POST['supplier_categories']) ? array_map('sanitize_text_field', $_POST['supplier_categories']) : array();
    update_post_meta($post_id, '_supplier_categories', $categories);
}
add_action('save_post_vendor', 'save_vendor_meta');


/**
 * Add Vendor selection to WooCommerce products
 */
function add_vendor_selection_to_products() {
    add_meta_box(
        'product_vendor_assignment',
        'Assign Vendor',
        'render_product_vendor_assignment',
        'product',
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'add_vendor_selection_to_products');

function render_product_vendor_assignment($post) {
    $current_vendor = get_post_meta($post->ID, '_product_vendor_id', true);
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'numberposts' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
        'post_status' => 'publish'
    ));

    wp_nonce_field('product_vendor_nonce', 'product_vendor_nonce');

    echo '<select name="product_vendor_id" id="product_vendor_id" style="width:100%;">';
    echo '<option value="">— Select Vendor —</option>';

    foreach ($vendors as $vendor) {
        $business_name = get_post_meta($vendor->ID, '_business_name', true);
        $display_name = $business_name ? $business_name : $vendor->post_title;

        echo '<option value="' . $vendor->ID . '" ' . selected($current_vendor, $vendor->ID, false) . '>'
            . esc_html($display_name) . '</option>';
    }

    echo '</select>';
    echo '<p class="description">Select the vendor associated with this product</p>';
}

function save_product_vendor_assignment($post_id) {
    if (!isset($_POST['product_vendor_nonce']) || !wp_verify_nonce($_POST['product_vendor_nonce'], 'product_vendor_nonce')) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['product_vendor_id'])) {
        update_post_meta($post_id, '_product_vendor_id', sanitize_text_field($_POST['product_vendor_id']));
    } else {
        delete_post_meta($post_id, '_product_vendor_id');
    }
}
add_action('save_post_product', 'save_product_vendor_assignment');


add_action('add_meta_boxes', 'add_vendor_orders_meta_box');

function add_vendor_orders_meta_box() {
    add_meta_box(
        'vendor_orders_box',
        'Vendor Orders',
        'render_vendor_orders_box',
        'vendor',
        'normal',
        'default'
    );
}

function render_vendor_orders_box($post) {
    $vendor_id = $post->ID;

    // Get all product IDs associated with this vendor
    $product_ids = get_posts([
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'meta_query' => [
            [
                'key' => '_product_vendor_id',
                'value' => $vendor_id,
                'compare' => '='
            ]
        ]
    ]);

    if (empty($product_ids)) {
        echo '<p>No products assigned to this vendor.</p>';
        return;
    }

    // Get orders that include these products
    global $wpdb;

    $placeholders = implode(',', array_fill(0, count($product_ids), '%d'));

    $query = "
        SELECT DISTINCT order_items.order_id
        FROM {$wpdb->prefix}woocommerce_order_items as order_items
        INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta as itemmeta
            ON order_items.order_item_id = itemmeta.order_item_id
        WHERE order_items.order_item_type = 'line_item'
        AND itemmeta.meta_key = '_product_id'
        AND itemmeta.meta_value IN ($placeholders)
    ";

    $prepared = $wpdb->prepare($query, $product_ids);
    $order_ids = $wpdb->get_col($prepared);

    if (empty($order_ids)) {
        echo '<p>No orders found for this vendor\'s products.</p>';
        return;
    }

    $export_url = admin_url('admin-post.php?action=export_vendor_orders_pdf&vendor_id=' . $vendor_id);
    echo '<a href="' . esc_url($export_url) . '" class="button button-primary" style="margin-bottom: 10px; margin-left: auto; display: block; width: fit-content; margin-top: 10px;" target="_blank">Export to PDF</a>';

    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead>
        <tr>
            <th>Order #</th>
            <th>Date</th>
            <th>Total</th>
            <th>Status</th>
        </tr>
      </thead>';
    echo '<tbody>';

    foreach ($order_ids as $order_id) {
        $order = wc_get_order($order_id);
        if (!$order) continue;

        $order_number = $order->get_order_number();
        $order_date = $order->get_date_created()->format('Y-m-d H:i');
        $order_total = $order->get_formatted_order_total();
        $order_status = wc_get_order_status_name($order->get_status());
        $order_url = get_edit_post_link($order_id);

        echo '<tr>';
        echo '<td><a href="' . esc_url($order_url) . '">#' . esc_html($order_number) . '</a></td>';
        echo '<td>' . esc_html($order_date) . '</td>';
        echo '<td>' . $order_total . '</td>';
        echo '<td>' . esc_html($order_status) . '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

// NEW FLOW ENDS HERE

require_once get_stylesheet_directory() . '/lib/dompdf/autoload.inc.php';

use Dompdf\Dompdf;

add_action('admin_post_export_vendor_orders_pdf', 'export_vendor_orders_pdf');

function export_vendor_orders_pdf() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized access');
    }

    if (!isset($_GET['vendor_id']) || !is_numeric($_GET['vendor_id'])) {
        wp_die('Invalid vendor');
    }

    $vendor_id = intval($_GET['vendor_id']);
    $vendor_title = get_the_title($vendor_id);

    // Get all products by this vendor
    $product_ids = get_posts([
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'meta_query' => [
            [
                'key' => '_product_vendor_id',
                'value' => $vendor_id,
                'compare' => '='
            ]
        ]
    ]);

    if (empty($product_ids)) {
        wp_die('No products found for this vendor.');
    }

    global $wpdb;
    $placeholders = implode(',', array_fill(0, count($product_ids), '%d'));
    $query = "
        SELECT DISTINCT order_items.order_id
        FROM {$wpdb->prefix}woocommerce_order_items AS order_items
        INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta AS itemmeta
        ON order_items.order_item_id = itemmeta.order_item_id
        WHERE order_items.order_item_type = 'line_item'
        AND itemmeta.meta_key = '_product_id'
        AND itemmeta.meta_value IN ($placeholders)
    ";
    $prepared = $wpdb->prepare($query, $product_ids);
    $order_ids = $wpdb->get_col($prepared);

    // Get logo URL
    $logo_id = get_theme_mod('custom_logo');
    $logo_url = $logo_id ? wp_get_attachment_image_url($logo_id, 'full') : '';

    // Build HTML
    $html = '
    <div style="text-align: center;">
        <img src="' . $logo_url . '" alt="Logo" style="max-width: 150px; margin-bottom: 20px;" />
        <h2 style="margin-bottom: 30px;">Orders for: ' . esc_html($vendor_title) . '</h2>
        <table border="1" cellpadding="10" cellspacing="0" style="width: 100%; font-size: 12px; border-collapse: collapse; margin: 0 auto; text-align: center;">
            <thead style="background-color: #f0f0f0;">
                <tr>
                    <th style="text-align: center;">Order #</th>
                    <th style="text-align: center;">Date</th>
                    <th style="text-align: center;">Total</th>
                    <th style="text-align: center;">Status</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($order_ids as $order_id) {
        $order = wc_get_order($order_id);
        if (!$order) continue;

        $html .= '<tr>';
        $html .= '<td>#' . $order->get_order_number() . '</td>';
        $html .= '<td>' . $order->get_date_created()->format('Y-m-d H:i') . '</td>';
        $html .= '<td>' . $order->get_formatted_order_total() . '</td>';
        $html .= '<td>' . wc_get_order_status_name($order->get_status()) . '</td>';
        $html .= '</tr>';
    }

    $html .= '</tbody></table>';

    // Generate PDF
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    $dompdf->stream(esc_html($vendor_title) . '-orders.pdf', ['Attachment' => true]);

    exit;
}

// CRON JOB STARTS HERE

add_filter('cron_schedules', function ($schedules) {
    $schedules['weekly'] = [
        // 'interval' => 604800, // 1 week
        'interval' => 10, // 10 seconds for testing
        'display'  => __('Once Weekly')
    ];
    return $schedules;
});

add_action('send_weekly_vendor_order_reports', 'email_weekly_vendor_reports');

function email_weekly_vendor_reports() {
    require_once get_stylesheet_directory() . '/lib/dompdf/autoload.inc.php';
    $vendors = get_posts([
        'post_type' => 'vendor',
        'numberposts' => -1,
        'post_status' => 'publish'
    ]);

    foreach ($vendors as $vendor) {
        $vendor_id = $vendor->ID;
        $vendor_name = get_the_title($vendor_id);
        $vendor_email = get_post_meta($vendor_id, '_email', true);

        if (!$vendor_email) continue;

        // Get products by vendor
        $product_ids = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'meta_query' => [
                [
                    'key' => '_product_vendor_id',
                    'value' => $vendor_id,
                    'compare' => '='
                ]
            ]
        ]);

        // Print the product IDs for checking
//         echo '<pre>';
//         print_r($product_ids);
//         echo '</pre>';

        if (empty($product_ids)) {
            wp_mail($vendor_email, 'Your Weekly Orders Report',
                "Dear $vendor_name,\n\nThere were no orders for your products this week.\n\nRegards,\nTeam",
                ['Content-Type: text/plain; charset=UTF-8']
            );
            continue;
        }

        // Get variation IDs for variable products
        global $wpdb;
        $variation_ids = [];
        if (!empty($product_ids)) {
            $placeholders = implode(',', array_fill(0, count($product_ids), '%d'));
            $variation_query = "
                SELECT ID FROM {$wpdb->prefix}posts
                WHERE post_type = 'product_variation'
                AND post_parent IN ($placeholders)
            ";
            $variation_ids = $wpdb->get_col($wpdb->prepare($variation_query, $product_ids));
        }

        // Combine product and variation IDs
        $all_product_ids = array_merge($product_ids, $variation_ids);

        if (empty($all_product_ids)) {
            wp_mail($vendor_email, 'Your Weekly Orders Report',
                "Dear $vendor_name,\n\nThere were no orders for your products this week.\n\nRegards,\nTeam",
                ['Content-Type: text/plain; charset=UTF-8']
            );
            continue;
        }

        // Check if HPOS is enabled
        $hpos_enabled = class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();

        $placeholders = implode(',', array_fill(0, count($all_product_ids), '%d'));

        if ($hpos_enabled) {
            $query = "
                SELECT DISTINCT oi.order_id
                FROM {$wpdb->prefix}woocommerce_order_items AS oi
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta AS oim
                    ON oi.order_item_id = oim.order_item_id
                INNER JOIN {$wpdb->prefix}wc_orders AS o
                    ON oi.order_id = o.id
                WHERE oi.order_item_type = 'line_item'
                  AND (oim.meta_key = '_product_id' OR oim.meta_key = '_variation_id')
                  AND oim.meta_value IN ($placeholders)
                  AND o.status IN ('wc-completed','wc-processing','wc-on-hold','wc-pending','wc-failed')
            ";
        } else {
            $query = "
                SELECT DISTINCT oi.order_id
                FROM {$wpdb->prefix}woocommerce_order_items AS oi
                INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta AS oim
                    ON oi.order_item_id = oim.order_item_id
                INNER JOIN {$wpdb->prefix}posts AS p
                    ON oi.order_id = p.ID
                WHERE oi.order_item_type = 'line_item'
                  AND (oim.meta_key = '_product_id' OR oim.meta_key = '_variation_id')
                  AND oim.meta_value IN ($placeholders)
                  AND p.post_type = 'shop_order'
                  AND p.post_status IN ('wc-completed','wc-processing','wc-on-hold','wc-pending','wc-failed')
            ";
        }

        $all_order_ids = $wpdb->get_col($wpdb->prepare($query, $all_product_ids));

        $site_timezone = new DateTimeZone(wp_timezone_string());

        $week_start = (new DateTime('monday this week', $site_timezone))->setTime(0, 0)->format('Y-m-d H:i:s');
        $week_end = (new DateTime('sunday this week', $site_timezone))->setTime(23, 59, 59)->format('Y-m-d H:i:s');

        $filtered_order_ids = [];
        foreach ($all_order_ids as $order_id) {
            $order = wc_get_order($order_id);
            if (!$order) continue;

            $created = $order->get_date_created();
            if (!$created) continue;

            $created_str = $created->setTimezone($site_timezone)->format('Y-m-d H:i:s');

            if ($created_str >= $week_start && $created_str <= $week_end) {
                $filtered_order_ids[] = $order_id;
            }
        }

        // Print the filtered order IDs for checking
//         echo '<pre>';
//         print_r($filtered_order_ids);
//         echo '</pre>';

        // No orders this week
        if (empty($filtered_order_ids)) {
            wp_mail($vendor_email, 'Your Weekly Orders Report',
                "Dear $vendor_name,\n\nThere were no orders for your products this week.\n\nRegards,\nTeam",
                ['Content-Type: text/plain; charset=UTF-8']
            );
            continue;
        }

        // Generate PDF with filtered orders
        $html = '
            <div style="text-align: center;">
                <img src="' . esc_url(wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'full')) . '" style="max-width: 150px; margin-bottom: 20px;" />
                <h2>Weekly Orders for ' . esc_html($vendor_name) . '</h2>
                <table border="1" cellpadding="10" cellspacing="0" style="width: 100%; font-size: 12px; border-collapse: collapse; margin: 0 auto; text-align: center;">
                    <thead style="background-color: #f0f0f0;">
                        <tr>
                            <th>Order #</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>';

        foreach ($filtered_order_ids as $order_id) {
            $order = wc_get_order($order_id);
            if (!$order) continue;

            $html .= '<tr>';
            $html .= '<td>#' . $order->get_order_number() . '</td>';
            $html .= '<td>' . $order->get_date_created()->format('Y-m-d H:i') . '</td>';
            $html .= '<td>' . $order->get_formatted_order_total() . '</td>';
            $html .= '<td>' . wc_get_order_status_name($order->get_status()) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table></div>';

        // Generate and save PDF
        $dompdf = new Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        $pdf_output = $dompdf->output();
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['path'] . "/{$vendor_name}-weekly-orders.pdf";
        file_put_contents($pdf_path, $pdf_output);

        // Send email
        wp_mail(
            $vendor_email,
            'Your Weekly Orders Report',
            "Dear $vendor_name,\n\nPlease find attached your order report for the week.\n\nRegards,\nTeam",
            ['Content-Type: text/plain; charset=UTF-8'],
            [$pdf_path]
        );
        echo 'dsfsdf';
 die;
        // Optionally delete file
        // unlink($pdf_path);
    }
}

// For manual testing
add_action('init', function () {
    if (isset($_GET['trigger_vendor_cron']) && current_user_can('manage_options')) {
        do_action('send_weekly_vendor_order_reports');
        exit('Weekly vendor report email sent.');

    }
});

// CRON JOB ENDS HERE

// Swal script

add_action('wp_enqueue_scripts', function() {
    // Enqueue SweetAlert2
    wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', [], '11.7.20', true);
    wp_enqueue_style('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css', [], '11.7.20');
    // Enqueue custom script
//    wp_enqueue_script('wc-custom-variations', get_template_directory_uri() . '/js/wc-custom-variations.js', ['jquery', 'sweetalert2'], null, true);
});

// Swal script

// Add custom shipping field with dropdown for the product
function add_shipping_cost_field_to_product() {
    // Dropdown for selecting shipping type
    woocommerce_wp_select(
        array(
            'id' => '_shipping_type',
            'label' => __('Shipping Type', 'woocommerce'),
            'desc_tip' => true,
            'description' => __('Select the shipping method for this product.', 'woocommerce'),
            'options' => array(
                'custom' => __('Custom Shipping', 'woocommerce'),
                'ups' => __('UPS Plugin', 'woocommerce'),
            ),
            'value' => get_post_meta(get_the_ID(), '_shipping_type', true) ?: 'custom', // Default to custom
        )
    );

    // Custom shipping cost field (shown only when Custom Shipping is selected)
    woocommerce_wp_text_input(
        array(
            'id' => '_shipping_cost',
            'label' => __('Shipping Cost ($)', 'woocommerce'),
            'desc_tip' => true,
            'description' => __('Enter the shipping cost for this product.', 'woocommerce'),
            'type' => 'number',
            'custom_attributes' => array(
                'step' => '0.01',
                'min' => '0',
            ),
            'placeholder' => '0.00',
            'wrapper_class' => 'custom-shipping-field', // For JavaScript targeting
        )
    );
}
add_action('woocommerce_product_options_general_product_data', 'add_shipping_cost_field_to_product');

// Save the custom shipping type and cost fields
function save_shipping_cost_field($post_id) {
    // Save shipping type
    $shipping_type = isset($_POST['_shipping_type']) ? sanitize_text_field($_POST['_shipping_type']) : 'custom';
    update_post_meta($post_id, '_shipping_type', $shipping_type);

    // Save shipping cost only if Custom Shipping is selected
    $shipping_cost = ($shipping_type === 'custom' && isset($_POST['_shipping_cost'])) ? floatval($_POST['_shipping_cost']) : 0;
    update_post_meta($post_id, '_shipping_cost', $shipping_cost);
}
add_action('woocommerce_process_product_meta', 'save_shipping_cost_field');

// JavaScript to toggle shipping cost field visibility
add_action('admin_footer', 'add_shipping_cost_toggle_script');
function add_shipping_cost_toggle_script() {
    if (get_post_type() === 'product') {
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                function toggleShippingCostField() {
                    var shippingType = $('#_shipping_type').val();
                    if (shippingType === 'custom') {
                        $('.custom-shipping-field').show();
                    } else {
                        $('.custom-shipping-field').hide();
                    }
                }

                // Run on page load
                toggleShippingCostField();

                // Run on dropdown change
                $('#_shipping_type').on('change', function() {
                    toggleShippingCostField();
                });
            });
        </script>
        <?php
    }
}

// Register custom shipping method
add_action('woocommerce_shipping_init', 'custom_product_shipping_method_init');

function custom_product_shipping_method_init() {
    if (!class_exists('WC_Custom_Product_Shipping')) {
        class WC_Custom_Product_Shipping extends WC_Shipping_Method {
            public function __construct($instance_id = 0) {
                $this->id = 'custom_product_shipping';
                $this->instance_id = absint($instance_id);
                $this->method_title = __('Custom Product Shipping', 'woocommerce');
                $this->method_description = __('Calculates shipping based on per-product shipping costs.', 'woocommerce');
                $this->supports = array(
                    'shipping-zones',
                    'instance-settings',
                    'instance-settings-modal',
                );
                $this->init();
            }

            public function init() {
                $this->init_form_fields();
                $this->enabled = $this->get_option('enabled', 'yes');
                $this->title = $this->get_option('title', __('Combined Shipping', 'woocommerce')); // Updated label
                add_action('woocommerce_update_options_shipping_' . $this->id, array($this, 'process_admin_options'));
            }

            public function init_form_fields() {
                $this->instance_form_fields = array(
                    'title' => array(
                        'title' => __('Title', 'woocommerce'),
                        'type' => 'text',
                        'description' => __('The title shown to customers during checkout.', 'woocommerce'),
                        'default' => __('Combined Shipping', 'woocommerce'),
                        'desc_tip' => true,
                    ),
                );
            }

            public function calculate_shipping($package = array()) {
                $total_shipping_cost = 0;
                $has_custom_shipping = false;
                $has_ups_shipping = false;

                foreach ($package['contents'] as $item_id => $values) {
                    $product_id = $values['product_id'];
                    $quantity = $values['quantity'];
                    $shipping_type = get_post_meta($product_id, '_shipping_type', true) ?: 'custom';
                    $shipping_cost = floatval(get_post_meta($product_id, '_shipping_cost', true));

                    error_log("Product ID: $product_id, Shipping Type: $shipping_type, Shipping Cost: $shipping_cost, Quantity: $quantity");

                    if ($shipping_type === 'custom') {
                        $has_custom_shipping = true;
                        $total_shipping_cost += $shipping_cost * $quantity;
                    } else if ($shipping_type === 'ups') {
                        $has_ups_shipping = true;
                    }
                }

                // Add the custom shipping rate if applicable
                if ($has_custom_shipping) {
                    error_log("Custom shipping detected, Total Custom Cost: $total_shipping_cost, Label: {$this->title}");
                    $this->add_rate(array(
                        'id' => $this->id . '_' . $this->instance_id,
                        'label' => $this->title,
                        'cost' => max(0, $total_shipping_cost),
                        'taxes' => WC_Tax::calc_shipping_tax($total_shipping_cost, WC_Tax::get_shipping_tax_rates()),
                        'package' => $package,
                    ));
                } else if (!$has_ups_shipping) {
                    error_log("No shipping types detected, skipping rate.");
                }
            }
        }
    }
}

// Add the custom shipping method to WooCommerce
add_filter('woocommerce_shipping_methods', 'add_custom_product_shipping_method');
function add_custom_product_shipping_method($methods) {
    $methods['custom_product_shipping'] = 'WC_Custom_Product_Shipping';
    return $methods;
}

// Display custom shipping cost in cart and checkout item details
function display_shipping_cost_in_cart($item_name, $cart_item, $cart_item_key) {
    $product_id = $cart_item['product_id'];
    $shipping_cost = floatval(get_post_meta($product_id, '_shipping_cost', true));

    if ($shipping_cost > 0) {
        $item_name .= '<br><small>' . __('Shipping Cost: ', 'woocommerce') . wc_price($shipping_cost) . '</small>';
    }

    return $item_name;
}
add_filter('woocommerce_cart_item_name', 'display_shipping_cost_in_cart', 10, 3);

// Prioritize and combine shipping rates
add_filter('woocommerce_package_rates', 'prioritize_custom_shipping_method', 100, 2);
function prioritize_custom_shipping_method($rates, $package) {
    $custom_rate_id = 'custom_product_shipping';
    $has_custom_shipping = false;
    $has_ups_shipping = false;
    $custom_total_cost = 0;

    // Check shipping types and calculate custom total
    foreach ($package['contents'] as $item_id => $values) {
        $product_id = $values['product_id'];
        $quantity = $values['quantity'];
        $shipping_type = get_post_meta($product_id, '_shipping_type', true) ?: 'custom';
        $shipping_cost = floatval(get_post_meta($product_id, '_shipping_cost', true));

        if ($shipping_type === 'custom') {
            $has_custom_shipping = true;
            $custom_total_cost += $shipping_cost * $quantity;
        } else if ($shipping_type === 'ups') {
            $has_ups_shipping = true;
        }
    }

    // Log all available rates for debugging
    $rate_details = array_map(function($rate) { return [$rate->id, $rate->label, $rate->cost]; }, $rates);
    error_log("All available rates at 12:50 AM PKT on July 25, 2025: " . print_r($rate_details, true));

    // If only custom shipping, prioritize custom shipping method
    if ($has_custom_shipping && !$has_ups_shipping) {
        $custom_rates = array();
        foreach ($rates as $rate_id => $rate) {
            if (strpos($rate_id, $custom_rate_id) !== false) {
                $custom_rates[$rate_id] = $rate;
            }
        }
        error_log("Prioritizing custom shipping rate, Total Cost: $custom_total_cost, Rates: " . print_r(array_keys($custom_rates), true));
        return !empty($custom_rates) ? $custom_rates : $rates;
    }
    // If only UPS shipping, filter to the lowest cost UPS method
    else if (!$has_custom_shipping && $has_ups_shipping) {
        $lowest_ups_rate = null;
        $ups_rates = array();
        foreach ($rates as $rate_id => $rate) {
            if (stripos($rate_id, 'ups') !== false || stripos($rate->label, 'ups') !== false) {
                $ups_rates[$rate_id] = $rate;
                if ($lowest_ups_rate === null || $rate->cost < $lowest_ups_rate->cost) {
                    $lowest_ups_rate = $rate;
                }
            }
        }
        error_log("All UPS-related rates: " . print_r(array_map(function($rate) { return [$rate->id, $rate->label, $rate->cost]; }, $ups_rates), true));
        error_log("Selected lowest UPS rate: " . ($lowest_ups_rate ? [$lowest_ups_rate->id, $lowest_ups_rate->label, $lowest_ups_rate->cost] : 'None'));
        return $lowest_ups_rate ? [$lowest_ups_rate->id => $lowest_ups_rate] : $rates;
    }
    // If mixed (custom and UPS), combine custom total with lowest UPS rate
    else if ($has_custom_shipping && $has_ups_shipping) {
        $lowest_ups_rate = null;
        $ups_rates = array();
        foreach ($rates as $rate_id => $rate) {
            if (stripos($rate_id, 'ups') !== false || stripos($rate->label, 'ups') !== false) {
                $ups_rates[$rate_id] = $rate;
                if ($lowest_ups_rate === null || $rate->cost < $lowest_ups_rate->cost) {
                    $lowest_ups_rate = $rate;
                }
            }
        }
        if ($lowest_ups_rate && $custom_total_cost > 0) {
            $combined_cost = max(0, $custom_total_cost + $lowest_ups_rate->cost);
            // Output to browser console for debugging
            add_action('wp_footer', function() use ($custom_total_cost, $lowest_ups_rate, $combined_cost) {
                ?>
                <script>
                    console.log("🚚 Combined Shipping Debug:");
                    console.log("🟦 Custom Total Cost: $<?php echo esc_js(number_format($custom_total_cost, 2)); ?>");
                    console.log("🟨 Lowest UPS Cost: $<?php echo esc_js(number_format($lowest_ups_rate->cost, 2)); ?>");
                    console.log("🟩 Combined Shipping Total: $<?php echo esc_js(number_format($combined_cost, 2)); ?>");
                </script>
                <?php
            });

            // Create a new shipping rate manually
            $combined_rate = new WC_Shipping_Rate();
            $combined_rate->id = $custom_rate_id . '_' . $lowest_ups_rate->instance_id; // Use a valid instance ID
            $combined_rate->label = __('Combined Shipping', 'woocommerce');
            $combined_rate->cost = $combined_cost;
            $combined_rate->taxes = WC_Tax::calc_shipping_tax($combined_cost, WC_Tax::get_shipping_tax_rates());
            $combined_rate->method_id = $custom_rate_id;
            $combined_rate->instance_id = $lowest_ups_rate->instance_id;
            error_log("Mixed shipping, Custom Cost: $custom_total_cost, Lowest UPS Cost: {$lowest_ups_rate->cost}, Combined Cost: $combined_cost");
            return [$combined_rate->id => $combined_rate];
        }
    }

    return $rates;
}



// custom login form
//add_action('woocommerce_checkout_create_order_line_item', 'add_recurring_order_to_checkout', 10, 4);

// Existing code from Step 2 above...

/**
 * Enqueue custom cart scripts
 */
/**
 * Updated Functions.php Code for Recurring Orders
 * Replace your existing recurring products code with this
 */

// FIXED: Correct paths for includes/ folder structure
add_action('init', 'load_recurring_orders_system');
function load_recurring_orders_system() {
    // Updated paths to match your includes/ folder structure
    if (file_exists(get_template_directory() . '/includes/database.php')) {
        require_once get_template_directory() . '/includes/database.php';
    }

    if (file_exists(get_template_directory() . '/includes/order-processing.php')) {
        require_once get_template_directory() . '/includes/order-processing.php';
    }

    if (file_exists(get_template_directory() . '/includes/admin-dashboard.php')) {
        require_once get_template_directory() . '/includes/admin-dashboard.php';
    }
}

/**
 * Display recurring products on checkout page
 */
add_action('woocommerce_before_checkout_form', 'display_recurring_products_checkout', 10);
function display_recurring_products_checkout() {
    global $wpdb;

    // Ensure WooCommerce is available
    if (!class_exists('WooCommerce') || !WC()->session) {
        return;
    }

    // Retrieve recurring products from WooCommerce session
    $recurring_products = WC()->session->get('recurring_products', []);

    // Only proceed if there are recurring products
    if (empty($recurring_products)) {
        return;
    }

    // Define custom table name
    $table_name = $wpdb->prefix . 'recurring_subscriptions';

    // Create table if it doesn't exist
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        user_id BIGINT(20) UNSIGNED NOT NULL,
        product_id BIGINT(20) UNSIGNED NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        schedule_type VARCHAR(50) NOT NULL,
        schedule_value VARCHAR(100) NOT NULL,
        duration VARCHAR(100) NOT NULL,
        image VARCHAR(255) DEFAULT '',
        order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(50) DEFAULT 'active',
        PRIMARY KEY (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Get current user ID (or 0 if guest)
    $user_id = get_current_user_id();

    // Insert recurring products into the database
    foreach ($recurring_products as $product_id => $product) {
        $product_name = isset($product['name']) ? sanitize_text_field($product['name']) : 'Unknown Product';
        $quantity = isset($product['quantity']) ? intval($product['quantity']) : 1;
        $price = isset($product['price']) ? floatval($product['price']) : 0;
        $schedule_type = isset($product['schedule']) ? sanitize_text_field($product['schedule']) : 'weekly';
        $schedule_value = ($schedule_type === 'weekly') ? (isset($product['week']) ? sanitize_text_field($product['week']) : 'Monday') : (isset($product['month']) ? sanitize_text_field($product['month']) : date('d M Y'));
        $duration = ($schedule_type === 'weekly') ? (isset($product['week_count']) ? intval($product['week_count']) . ' weeks' : '1 week') : (isset($product['month_count']) ? intval($product['month_count']) . ' months' : '1 month');
        $image = isset($product['image']) && !empty($product['image']) ? esc_url_raw($product['image']) : wc_placeholder_img_src();

        // Check if this product is already inserted for this user to avoid duplicates
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND product_id = %d AND product_name = %s AND schedule_type = %s AND schedule_value = %s",
            $user_id,
            $product_id,
            $product_name,
            $schedule_type,
            $schedule_value
        ));

        if (!$existing) {
            $wpdb->insert(
                $table_name,
                [
                    'user_id' => $user_id,
                    'product_id' => $product_id,
                    'product_name' => $product_name,
                    'quantity' => $quantity,
                    'price' => $price,
                    'schedule_type' => $schedule_type,
                    'schedule_value' => $schedule_value,
                    'duration' => $duration,
                    'image' => $image,
                    'status' => 'active'
                ],
                ['%d', '%d', '%s', '%d', '%f', '%s', '%s', '%s', '%s', '%s']
            );
        }
    }

    // Get shipping total or fallback to $47
    $shipping_fee = WC()->cart->get_shipping_total() ? wc_price(WC()->cart->get_shipping_total()) : '$47';

    // Output the recurring products table
    ?>
    <div class="custom-checkout-page" style="display: block !important; margin-bottom: 20px;">
        <h2>🔄 Recurring Order Summary</h2>
        <div class="alert alert-info" style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
            <strong>📅 Automatic Orders:</strong> These products will be automatically ordered according to your selected schedule after checkout completion.
        </div>

        <table id="checkoutTable" class="recurring-checkout-table">
            <thead>
            <tr>
                <th>Image</th>
                <th>Product</th>
                <th>Quantity</th>
                <th>Schedule</th>
                <th>Duration</th>
                <th>Shipping</th>
                <th>Total per Order</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($recurring_products as $product_id => $product) :
                // Enhanced data processing with fallbacks
                $product_name = isset($product['name']) ? esc_html($product['name']) : 'Unknown Product';
                $quantity = isset($product['quantity']) ? intval($product['quantity']) : 1;
                $price = isset($product['price']) ? floatval($product['price']) : 0;

                // Better schedule display
                $schedule_type = isset($product['schedule']) ? $product['schedule'] : 'weekly';
                $schedule_value = '';
                $duration = '';

                if ($schedule_type === 'weekly') {
                    $schedule_value = isset($product['week']) ? $product['week'] : 'Monday';
                    $duration = isset($product['week_count']) ? $product['week_count'] . ' weeks' : '1 week';
                    $schedule_display = "Every {$schedule_value}";
                } else {
                    $schedule_value = isset($product['month']) ? $product['month'] : date('d M Y');
                    $duration = isset($product['month_count']) ? $product['month_count'] . ' months' : '1 month';
                    $schedule_display = "Monthly on {$schedule_value}";
                }

                $image = isset($product['image']) && !empty($product['image']) ? esc_url($product['image']) : wc_placeholder_img_src();
                $total = $quantity * $price;
                ?>
                <tr>
                    <td><img src="<?php echo $image; ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;" alt="<?php echo $product_name; ?>"></td>
                    <td><strong><?php echo $product_name; ?></strong></td>
                    <td><?php echo $quantity; ?></td>
                    <td><?php echo $schedule_display; ?></td>
                    <td><?php echo $duration; ?></td>
                    <td><?php echo $shipping_fee; ?></td>
                    <td><strong><?php echo wc_price($total); ?></strong></td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>

        <div class="recurring-info" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
            <small>
                <strong>ℹ️ Note:</strong> You can manage, pause, or cancel your recurring orders anytime from your account dashboard after checkout.
            </small>
        </div>
    </div>
    <style>
        .custom-checkout-page {
            margin-bottom: 25px;
            padding: 20px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .recurring-checkout-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .recurring-checkout-table th, .recurring-checkout-table td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            vertical-align: middle;
        }
        .recurring-checkout-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .recurring-checkout-table img {
            display: block;
            margin: 0 auto;
        }
        .recurring-checkout-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .recurring-checkout-table tr:hover {
            background-color: #f5f5f5;
        }
    </style>
    <?php
}

/**
 * Shortcode version for checkout display
 */
// Ensure WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    return;
}

/**
 * Register the recurring products shortcode
 */
function custom_recurring_products_checkout_shortcode() {
    // Ensure WooCommerce session is available
    if ( ! WC()->session ) {
        return '<p style="color: red;">WooCommerce session not available.</p>';
    }

    // Get recurring products from WooCommerce session
    $recurring_products = WC()->session->get( 'recurring_products', [] );

    // Return empty if no products
    if ( empty( $recurring_products ) ) {
        return '<div class="no-recurring-products" style="padding: 20px; text-align: center; background: #f8f9fa; border-radius: 4px;">
                    <p>No recurring products selected.</p>
                    <a href="' . wc_get_cart_url() . '" class="button">Return to Cart</a>
                </div>';
    }

    // Start the output with exact modal structure
    $output = '<div class="custom-checkout-page-shortcode">
                <div class="modal-header-style">
                    <h5 class="modal-title">Recurring Order Summary</h5>
                </div>
                <div class="modal-body-style">
                    <div id="summaryTable">
                        <div class="summary_table_details">';

    foreach ( $recurring_products as $product ) {
        // Get product data
        $product_name = esc_html( $product['name'] ?? 'Unknown Product' );
        $product_image = esc_url( $product['image'] ?? '' );
        $product_quantity = intval( $product['quantity'] ?? 1 );
        $product_subtotal = floatval( $product['subtotal'] ?? 0 );
        $schedule_type = $product['schedule'] ?? 'weekly';
        $period_count = intval( $product['period_count'] ?? 1 );

        // Determine schedule display text
        if ( $schedule_type === 'weekly' ) {
            $schedule_display_text = $product['week'] ?? 'Monday';
            $period_label = 'Number of Weeks:';
        } else {
            $schedule_display_text = $product['month'] ?? date( 'd M Y' );
            $period_label = 'Number of Months:';
        }

        // Calculate totals exactly like the JavaScript
        $shipping_per_delivery = 47;
        $total_deliveries = $period_count;
        $total_shipping = $shipping_per_delivery * $total_deliveries;
        $total_product_cost = $product_subtotal * $total_deliveries;
        $grand_total = $total_product_cost + $total_shipping;

        // Create exact same HTML structure as JavaScript
        $output .= '<div class="order_details_wrapper">
                        <div class="image_upper_wrap_order_details">
                            <div class="order_details_wrapper_img_wrap">
                                <img src="' . $product_image . '" class="img-fluid" alt="' . $product_name . '"
                                     onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';">
                                <div style="display:none; padding: 10px; text-align: center;">📦</div>
                            </div>
                        </div>
                        <div class="order_details_wrapper_details">
                            <h6 style="margin-bottom: 15px; font-weight: bold;">' . $product_name . '</h6>
                            <div class="quantity_weeks_shipping_fee_wrap">
                                <div><span>Quantity per delivery:</span><label>' . $product_quantity . '</label></div>
                                <div><span>' . $period_label . '</span><label>' . $period_count . '</label></div>
                                <div><span>' . ($schedule_type === 'weekly' ? 'Day of Week:' : 'Date of Month:') . '</span><label>' . $schedule_display_text . '</label></div>
                                <div><span>Total Deliveries:</span><label>' . $total_deliveries . '</label></div>
                                <div><span>Shipping per delivery:</span><label>$' . $shipping_per_delivery . '</label></div>
                                <div><span>Total Shipping:</span><label>$' . $total_shipping . '</label></div>
                                <div><span>Product Cost (' . $total_deliveries . ' × $' . number_format( $product_subtotal, 2 ) . '):</span><label>$' . number_format( $total_product_cost, 2 ) . '</label></div>
                            </div>
                            <div class="total_order_det_count_wrap">
                                <span>Grand Total:</span>
                                <label>$' . number_format( $grand_total, 2 ) . '</label>
                            </div>
                        </div>
                    </div>';
    }

    $output .= '        </div>
                    </div>
                </div>
            </div>';

    // Add CSS to match your existing modal styles
    $output .= '<style>
        .custom-checkout-page-shortcode {
            margin-bottom: 30px;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .modal-header-style {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            padding: 1rem 1rem;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: calc(0.5rem - 1px);
            border-top-right-radius: calc(0.5rem - 1px);
        }

        .modal-header-style .modal-title {
            margin-bottom: 0;
            line-height: 1.5;
            font-size: 1.25rem;
            font-weight: 500;
        }

        .modal-body-style {
            position: relative;
            flex: 1 1 auto;
            padding: 1rem;
        }

        .order_details_wrapper {
            display: flex;
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .image_upper_wrap_order_details {
            flex-shrink: 0;
            margin-right: 20px;
        }

        .order_details_wrapper_img_wrap {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
        }

        .order_details_wrapper_img_wrap img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .order_details_wrapper_details {
            flex: 1;
        }

        .order_details_wrapper_details h6 {
            color: #333;
            font-size: 1.1rem;
        }

        .quantity_weeks_shipping_fee_wrap {
            margin-bottom: 15px;
        }

        .quantity_weeks_shipping_fee_wrap > div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #eee;
        }

        .quantity_weeks_shipping_fee_wrap > div:last-child {
            border-bottom: none;
        }

        .quantity_weeks_shipping_fee_wrap span {
            font-weight: 500;
            color: #495057;
        }

        .quantity_weeks_shipping_fee_wrap label {
            font-weight: 600;
            color: #28a745;
            margin-bottom: 0;
        }

        .total_order_det_count_wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: #FD7425;
            color: #fff;
            border-radius: 6px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .total_order_det_count_wrap span,
        .total_order_det_count_wrap label {
            color: #fff;
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .order_details_wrapper {
                flex-direction: column;
            }

            .image_upper_wrap_order_details {
                margin-right: 0;
                margin-bottom: 15px;
                text-align: center;
            }

            .quantity_weeks_shipping_fee_wrap > div {
                flex-direction: column;
                text-align: center;
                padding: 10px 0;
            }
        }
    </style>';

    return $output;
}

/**
 * Add the recurring products checkout shortcode
 */
add_action( 'init', 'register_recurring_shortcode' );
function register_recurring_shortcode() {
    add_shortcode( 'custom_recurring_checkout', 'custom_recurring_products_checkout_shortcode' );
}

/**
 * Save recurring products to the WooCommerce session
 */
add_action('wp_ajax_save_recurring_products', 'save_recurring_products_to_session');
add_action('wp_ajax_nopriv_save_recurring_products', 'save_recurring_products_to_session');

function save_recurring_products_to_session() {
    check_ajax_referer('save_recurring_products_nonce', 'nonce');

    if ( ! isset( $_POST['products'] ) || empty( $_POST['products'] ) ) {
        wp_send_json_error( [ 'message' => 'No products selected.' ] );
    }

    $products = $_POST['products'];
    $schedule_type = sanitize_text_field( $_POST['schedule_type'] ?? '' );
    $schedule_day = sanitize_text_field( $_POST['schedule_day'] ?? '' );
    $schedule_date = sanitize_text_field( $_POST['schedule_date'] ?? '' );
    $period_count = intval( $_POST['period_count'] ?? ( $_POST['period_count_monthly'] ?? 1 ) );

    $recurring_data = [];

    foreach ( $products as $key => $item ) {
        $recurring_data[] = [
            'key'           => sanitize_text_field( $item['key'] ),
            'product_id'    => intval( $item['product_id'] ),
            'name'          => sanitize_text_field( $item['name'] ),
            'price'         => floatval( $item['price'] ),
            'image'         => esc_url_raw( $item['image'] ),
            'quantity'      => intval( $item['quantity'] ),
            'subtotal'      => floatval( $item['subtotal'] ),
            'schedule'      => $schedule_type,
            'period_count'  => $period_count,
            'week'          => $schedule_type === 'weekly' ? $schedule_day : '',
            'month'         => $schedule_type === 'monthly' ? $schedule_date : '',
        ];
    }

    // Save to WooCommerce session
    WC()->session->set( 'recurring_products', $recurring_data );

    wp_send_json_success( [
        'data' => $recurring_data,
        'message' => 'Recurring products saved to session.'
    ] );
}

function add_elementor_template_to_checkout() {
    // Add your Elementor template logic here if needed
    // For example: echo do_shortcode('[elementor-template id="YOUR_TEMPLATE_ID"]');
}

// Hook into AJAX request for logged-in users
add_action('wp_ajax_save_recurring_products', 'save_recurring_products_callback');
// Hook into AJAX request for non-logged-in users
add_action('wp_ajax_nopriv_save_recurring_products', 'save_recurring_products_callback');

function save_recurring_products_callback() {
    // Check if the nonce is set and valid
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_recurring_products_nonce')) {
        error_log('Nonce verification failed.');
        wp_send_json_error(array('message' => 'Nonce verification failed.'));
        wp_die();
    }

    // Log the incoming data for debugging
    error_log('Received POST data: ' . print_r($_POST, true));

    // Check if products are passed in the request
    if (!isset($_POST['products']) || !is_array($_POST['products']) || empty($_POST['products'])) {
        error_log('No products found in POST data.');
        wp_send_json_error(array('message' => 'No recurring products found.'));
        wp_die();
    }

    $recurring_products = array();
    $shipping_fee = WC()->cart->get_shipping_total() ? wc_price(WC()->cart->get_shipping_total()) : '$47';

    foreach ($_POST['products'] as $cart_item_key => $product_data) {
        // Sanitize and validate input
        $key = sanitize_text_field($cart_item_key);
        $product_id = isset($product_data['product_id']) ? absint($product_data['product_id']) : 0;
        $name = isset($product_data['name']) ? sanitize_text_field($product_data['name']) : 'Unknown Product';
        $price = isset($product_data['price']) ? floatval($product_data['price']) : 0;
        $quantity = isset($product_data['quantity']) ? absint($product_data['quantity']) : 1;
        $image = isset($product_data['image']) ? esc_url_raw($product_data['image']) : wc_placeholder_img_src();
        $schedule = isset($product_data['schedule']) ? sanitize_text_field($product_data['schedule']) : 'weekly';
        $week = isset($product_data['week']) ? sanitize_text_field($product_data['week']) : '';
        $month = isset($product_data['month']) ? sanitize_text_field($product_data['month']) : '';
        $week_count = isset($product_data['week_count']) ? absint($product_data['week_count']) : 1;
        $month_count = isset($product_data['month_count']) ? absint($product_data['month_count']) : 1;

        // Determine schedule and period count
        $display_schedule = $schedule === 'weekly' ? $week : $month;
        $period_count = $schedule === 'weekly' ? $week_count : $month_count;

        // Calculate total
        $total = wc_price($price * $quantity);

        // Add product to recurring_products array
        $recurring_products[] = array(
            'key' => $key,
            'product_id' => $product_id,
            'name' => $name,
            'price' => $price,
            'quantity' => $quantity,
            'image' => $image,
            'schedule' => $display_schedule,
            'period_count' => $period_count,
            'week' => $week,
            'month' => $month,
            'shipping_fee' => $shipping_fee,
            'total' => $total
        );
    }

    // Store in WooCommerce session
    WC()->session->set('recurring_products', $recurring_products);

    // Log the processed products
    error_log('Processed recurring products: ' . print_r($recurring_products, true));

    // Return success response
    wp_send_json_success(array(
        'message' => 'Recurring order saved successfully.',
        'products' => $recurring_products
    ));

    wp_die();
}
//end recurring
//function add_elementor_template_to_checkout() {
//    // Check if Elementor is active
//    if (did_action('elementor/loaded')) {
//        echo do_shortcode('[elementor-template id="326"]');
//    }
//}


function custom_woocommerce_logout_redirect() {
    wp_redirect( home_url('/Home/') );
    exit;
}
add_action('wp_logout', 'custom_woocommerce_logout_redirect');


//custom email
// 1. FIRST: Save cart item data to order item meta when order is created
add_action('woocommerce_checkout_create_order_line_item', 'save_custom_bundle_to_order_item', 10, 4);
function save_custom_bundle_to_order_item($item, $cart_item_key, $values, $order) {
    // Save the custom bundle data to order item meta
    if (isset($values['custom_bundle']) && !empty($values['custom_bundle'])) {
        $item->add_meta_data('_custom_bundle', $values['custom_bundle']);
        error_log('Saved custom_bundle to order item meta: ' . print_r($values['custom_bundle'], true));
    }

    // Also save custom bundle attributes if they exist
    if (isset($values['custom_bundle_attributes']) && !empty($values['custom_bundle_attributes'])) {
        $item->add_meta_data('_custom_bundle_attributes', $values['custom_bundle_attributes']);
    }
}

// 2. SECOND: Updated vendor email function that uses the saved data
add_action('woocommerce_new_order', 'send_vendor_email_on_product_purchase', 10, 1);
function send_vendor_email_on_product_purchase($order_id) {
    // Get the order object
    $order = wc_get_order($order_id);

    // Loop through the items in the order
    foreach ($order->get_items() as $item_id => $item) {
        // Get the product ID from the order item
        $product_id = $item->get_product_id();

        // Get the vendor ID associated with the product
        $vendor_id = get_post_meta($product_id, '_product_vendor_id', true);

        // If the product does not have an assigned vendor, skip this item
        if (!$vendor_id) {
            continue;
        }

        // Retrieve vendor email
        $vendor_email = get_post_meta($vendor_id, '_email', true);

        // If vendor email is found, send the email
        if ($vendor_email) {
            // Set up the email subject and message
            $subject = "New Order for Your Product!";
            $message = '<html><body style="font-family: Arial, sans-serif; color: #333;">';
            $message .= '<h2 style="color: #FD7425;">New Order for Your Product!</h2>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;">Dear Vendor,</p>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;">Your product has been purchased in an order. Below are the details:</p>';

            // Start the table for product details
            $message .= '<table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse; width: 100%; max-width: 600px; margin-top: 20px; font-family: Arial, sans-serif; color: #333;">';
            $message .= '<thead><tr style="background-color: #FD7425;"><th style="text-align:left; padding: 10px;">Product</th><th style="text-align:left; padding: 10px;">Quantity</th></tr></thead><tbody>';

            // Add product details to the email table
            $product_name = $item->get_name();
            $product = wc_get_product($product_id);
            $order_unit = $product ? get_post_meta($product->get_id(), '_order_unit', true) : 'Unit';
            $order_unit = $order_unit ?: 'Unit';

            // Debug: Log all item meta
            error_log('Order item meta data for item ' . $item_id . ': ');
            $meta_data = $item->get_meta_data();
            foreach ($meta_data as $meta) {
                error_log('Meta Key: ' . $meta->key . ' | Meta Value: ' . print_r($meta->value, true));
            }

            // Get the custom bundle data from order item meta
            $custom_bundle = $item->get_meta('_custom_bundle', true);

            if (empty($custom_bundle)) {
                // Fallback: try without underscore
                $custom_bundle = $item->get_meta('custom_bundle', true);
            }

            error_log('Retrieved custom_bundle from order meta: ' . print_r($custom_bundle, true));

            // Initialize bundle details
            $bundle_details = [];

            if (!empty($custom_bundle) && is_array($custom_bundle)) {
                // Group variations by table (using the same logic as your cart display)
                $tables_data = [];

                // Organize data by table index - same structure as your working cart code
                foreach ($custom_bundle as $variation_id => $quantities) {
                    if (is_array($quantities)) {
                        foreach ($quantities as $table_index => $quantity) {
                            if (!isset($tables_data[$table_index])) {
                                $tables_data[$table_index] = [];
                            }
                            if ($quantity > 0) {
                                $tables_data[$table_index][$variation_id] = $quantity;
                            }
                        }
                    }
                }

                // Sort tables by index
                ksort($tables_data);

                // Process each table - exact same logic as your cart display
                foreach ($tables_data as $table_index => $variations) {
                    $table_label = ucfirst($order_unit) . ' ' . ($table_index + 1);
                    $table_variations = [];
                    $table_total_qty = 0;

                    foreach ($variations as $variation_id => $quantity) {
                        $variation = wc_get_product($variation_id);
                        if (!$variation) {
                            error_log('Invalid variation ID: ' . $variation_id);
                            continue;
                        }

                        // Get variation attributes and format them - same as cart display
                        $attributes = $variation->get_variation_attributes();
                        $attribute_names = [];

                        if (!empty($attributes)) {
                            foreach ($attributes as $taxonomy => $value) {
                                // Clean up taxonomy name
                                $clean_taxonomy = str_replace('attribute_', '', $taxonomy);
                                $label = wc_attribute_label($clean_taxonomy, $variation);

                                // Handle empty values
                                if (empty($value)) {
                                    $value = __('Any', 'woocommerce');
                                } else {
                                    // Get the proper term name if it's a taxonomy
                                    if (taxonomy_exists($clean_taxonomy)) {
                                        $term = get_term_by('slug', $value, $clean_taxonomy);
                                        if ($term && !is_wp_error($term)) {
                                            $value = $term->name;
                                        }
                                    }
                                }

                                $attribute_names[] = $label . ': ' . esc_html($value);
                            }
                            $variation_name = implode(' / ', $attribute_names);
                        } else {
                            $variation_name = $variation->get_name();
                        }

                        if ($quantity > 0) {
                            $table_variations[] = sprintf('%s (Qty: %d)', $variation_name, $quantity);
                            $table_total_qty += $quantity;
                        }

                        error_log('EMAIL - Table ' . $table_index . ' - Variation ID: ' . $variation_id . ' | Name: ' . $variation_name . ' | Quantity: ' . $quantity);
                    }

                    // Add table data to bundle details
                    if (!empty($table_variations)) {
                        $bundle_details[] = [
                            'table_label' => $table_label,
                            'variations' => $table_variations,
                            'total_qty' => $table_total_qty
                        ];
                    }
                }
            }

            // Add product row
            $message .= '<tr>';
            $message .= '<td style="padding: 10px;">' . esc_html($product_name);

            // Format bundle details for email - same structure as cart display
            if (!empty($bundle_details)) {
                $message .= '<div class="bundle-contents-wrapper" style="margin-top: 10px;">';
                foreach ($bundle_details as $table_data) {
                    $message .= '<div class="bundle-table-section" style="margin-bottom: 15px;">';
                    $message .= '<strong style="display: block; margin-bottom: 5px; color: #FD7425;">' . esc_html($table_data['table_label']) . '</strong>';
                    $message .= '<ul style="list-style: none; padding: 0; margin: 0; padding-left: 10px;">';
                    foreach ($table_data['variations'] as $variation_detail) {
                        $message .= '<li style="font-size: 14px; margin-bottom: 3px;">• ' . esc_html($variation_detail) . '</li>';
                    }
                    $message .= '</ul>';
                    $message .= '<span style="display: block; margin-top: 5px; font-style: italic; color: #666;">Total: ' . $table_data['total_qty'] . '</span>';
                    $message .= '</div>';
                }
                $message .= '</div>';
            } else {
                $message .= '<div style="color: #888; font-style: italic; margin-top: 10px;">No bundle details available.</div>';
                error_log('No bundle details found for product: ' . $product_name . ' (ID: ' . $product_id . ')');
            }

            $message .= '</td>';
            $message .= '<td style="padding: 10px;">' . esc_html($item->get_quantity()) . '</td>';
            $message .= '</tr>';

            $message .= '</tbody></table>';

            // Add order details below the product table
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Order Number:</strong> ' . $order->get_order_number() . '</p>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Order Date:</strong> ' . $order->get_date_created()->format('Y-m-d H:i') . '</p>';

            // Add customer information
            $message .= '<h3 style="color: #FD7425; margin-top: 20px;">Customer Information</h3>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Name:</strong> ' . esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()) . '</p>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Email:</strong> ' . esc_html($order->get_billing_email()) . '</p>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Phone:</strong> ' . esc_html($order->get_billing_phone()) . '</p>';
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Billing Address:</strong><br>' .
                esc_html($order->get_formatted_billing_address()) . '</p>';

            // Add shipping address if different from billing
            if ($order->get_formatted_shipping_address()) {
                $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;"><strong>Shipping Address:</strong><br>' .
                    esc_html($order->get_formatted_shipping_address()) . '</p>';
            }

            // Add closing message
            $message .= '<p style="font-family: Poppins, sans-serif; font-size: 16px;">Best regards,<br>Your Store</p>';
            $message .= '</body></html>';

            // Email headers (set content type to HTML)
            $headers = array('Content-Type: text/html; charset=UTF-8');

            // Send the email to the vendor
            wp_mail($vendor_email, $subject, $message, $headers);

            error_log('Vendor email sent to: ' . $vendor_email . ' for product: ' . $product_name);
        }
    }
}
//custom email code end

// SHOW custom fields in user profile
add_action('show_user_profile', 'custom_show_extra_user_fields');
add_action('edit_user_profile', 'custom_show_extra_user_fields');

function custom_show_extra_user_fields($user) {
    ?>
    <h3>Business Registration Details</h3>
    <table class="form-table">
        <tr>
            <th><label for="input_box_1752098207544">Name of Business</label></th>
            <td>
                <input type="text" name="input_box_1752098207544" id="input_box_1752098207544" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098207544', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098115">Name of Owner</label></th>
            <td>
                <input type="text" name="input_box_1752098115" id="input_box_1752098115" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098115', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098646012">Phone Number</label></th>
            <td>
                <input type="text" name="input_box_1752098646012" id="input_box_1752098646012" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098646012', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098487">Street Address</label></th>
            <td>
                <input type="text" name="input_box_1752098487" id="input_box_1752098487" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098487', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098548">City</label></th>
            <td>
                <input type="text" name="input_box_1752098548" id="input_box_1752098548" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098548', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098575">State</label></th>
            <td>
                <input type="text" name="input_box_1752098575" id="input_box_1752098575" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098575', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098665275">Weekly Ave. Supply Purchases</label></th>
            <td>
                <input type="text" name="input_box_1752098665275" id="input_box_1752098665275" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098665275', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098705706">Social Media Links</label></th>
            <td>
                <input type="text" name="input_box_1752098705706" id="input_box_1752098705706" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098705706', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098728441">Resale #</label></th>
            <td>
                <input type="text" name="input_box_1752098728441" id="input_box_1752098728441" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098728441', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="input_box_1752098717389">Product of Interest</label></th>
            <td>
                <input type="text" name="input_box_1752098717389" id="input_box_1752098717389" value="<?php echo esc_attr(get_user_meta($user->ID, 'input_box_1752098717389', true)); ?>" class="regular-text" />
            </td>
        </tr>
    </table>
    <?php
}

// SAVE custom fields on profile update
add_action('personal_options_update', 'custom_save_extra_user_fields');
add_action('edit_user_profile_update', 'custom_save_extra_user_fields');

function custom_save_extra_user_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) return false;

    update_user_meta($user_id, 'input_box_1752098207544', sanitize_text_field($_POST['input_box_1752098207544']));
    update_user_meta($user_id, 'input_box_1752098115', sanitize_text_field($_POST['input_box_1752098115']));
    update_user_meta($user_id, 'input_box_1752098646012', sanitize_text_field($_POST['input_box_1752098646012']));
    update_user_meta($user_id, 'input_box_1752098487', sanitize_text_field($_POST['input_box_1752098487']));
    update_user_meta($user_id, 'input_box_1752098548', sanitize_text_field($_POST['input_box_1752098548']));
    update_user_meta($user_id, 'input_box_1752098575', sanitize_text_field($_POST['input_box_1752098575']));
    update_user_meta($user_id, 'input_box_1752098665275', sanitize_text_field($_POST['input_box_1752098665275']));
    update_user_meta($user_id, 'input_box_1752098705706', sanitize_text_field($_POST['input_box_1752098705706']));
    update_user_meta($user_id, 'input_box_1752098728441', sanitize_text_field($_POST['input_box_1752098728441']));
    update_user_meta($user_id, 'input_box_1752098717389', sanitize_text_field($_POST['input_box_1752098717389']));
}

//recuring condition

