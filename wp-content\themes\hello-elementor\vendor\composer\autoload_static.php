<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit2e54322bb8173f91f8ffc7ba2d1178ac
{
    public static $prefixLengthsPsr4 = array (
        'E' => 
        array (
            'Elementor\\WPNotificationsPackage\\' => 33,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Elementor\\WPNotificationsPackage\\' => 
        array (
            0 => __DIR__ . '/..' . '/elementor/wp-notifications-package/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit2e54322bb8173f91f8ffc7ba2d1178ac::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit2e54322bb8173f91f8ffc7ba2d1178ac::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit2e54322bb8173f91f8ffc7ba2d1178ac::$classMap;

        }, null, ClassLoader::class);
    }
}
