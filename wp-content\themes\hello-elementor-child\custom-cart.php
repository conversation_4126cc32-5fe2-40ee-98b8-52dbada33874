<?php
/*
Template Name: Custom Cart
*/

// Include the WordPress header
get_header();
?>

    <!-- <PERSON><PERSON><PERSON> and Flatpickr CDN Links -->
    <head>
        <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-JcKb8q3iqJ61gNV9KGb8thSsNjpSL0n8PARn9HuZOnIxN0hoP+VmmDGMN5t9UJ0Z" crossorigin="anonymous">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://code.jquery.com/jquery-3.5.1.min.js" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5PtkFExj5u9bOyDDn5a+3Kjt6P9l2e8" crossorigin="anonymous"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js" integrity="sha384-B4gt1jrGC7Jh4AgTPSdUtOBvfO8shuf57BaghqFfPlYxofvL8/KUEfYiJOMMV+rV" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    </head>

    <!-- Banner Shortcode -->
<?php echo do_shortcode('[elementor-template id="326"]'); ?>

<?php if ( WC()->cart->get_cart_contents_count() > 0 ) : ?>
    <!-- Cart Content -->
    <section class="cart_page">
        <div class="container">
            <div class="row">
                <div class="col-md-12 cart-content">
                    <!-- Cart Table -->
                    <div class="cart-table-section">
                        <table class="table-header">
                            <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Subtotal</th>
                                <th>Remove</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) : ?>
                                <?php
                                $product = $cart_item['data'];
                                $product_id = $product->get_id();
                                $product_name = $product->get_name();
                                $product_price = $product->get_price();
                                $product_quantity = $cart_item['quantity'];
                                $product_subtotal = $cart_item['line_total'];
                                $product_image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'thumbnail');
                                ?>
                                <tr>
                                    <td class="product-info">
                                        <div class="product-image">
                                            <?php if ($product_image) : ?>
                                                <img src="<?php echo esc_url($product_image[0]); ?>" alt="<?php echo esc_attr($product_name); ?>">
                                            <?php else : ?>
                                                <div class="placeholder-image">📦</div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="product-name"><?php echo esc_html($product_name); ?></div>
                                    </td>
                                    <td class="price"><?php echo wc_price($product_price); ?></td>
                                    <td class="quantity"><?php echo esc_html($product_quantity); ?></td>
                                    <td class="subtotal"><?php echo wc_price($product_subtotal); ?></td>
                                    <td class="remove">
                                        <a href="<?php echo esc_url(wc_get_cart_remove_url($cart_item_key)); ?>" class="remove-btn">✕</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_row">
                        <div class="first_box">
                            <!-- Cart Total -->
                            <h3>Cart Total</h3>
                            <div class="cart-total">
                                <div class="total-row">
                                    <span>Sub Total</span>
                                    <span><?php echo WC()->cart->get_cart_subtotal(); ?></span>
                                </div>
                                <div class="total-row">
                                    <span class="shipping-info">
                                        Estimated Shipping Fee
                                        <div class="info-icon" title="Why is my shipping high? Because we source from multiple suppliers.">i</div>
                                    </span>
                                    <span><?php echo WC()->cart->get_shipping_total() ? wc_price(WC()->cart->get_shipping_total()) : '$47'; ?></span>
                                </div>
                                <div class="total-row total-final">
                                    <span>Total</span>
                                    <span><?php echo WC()->cart->get_total(); ?></span>
                                </div>
                            </div>
                            <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="checkout-btn">Proceed To Checkout</a>
                        </div>
                        <div class="second_box">
                            <!-- Recurring Order Option -->
                            <div class="recurring-order">
                                <h3>Recurring Order</h3>
                                <p>Do you want any product as a repeat order?</p>
                                <div class="radio-group">
                                    <div class="radio-option">
                                        <input type="radio" id="yes" name="recurring-order" value="yes" data-toggle="modal" data-target="#recurringModal">
                                        <label for="yes">Yes</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="no" name="recurring-order" value="no" checked>
                                        <label for="no">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap Modal for Recurring Order Details -->
    <div class="modal fade" id="recurringModal" tabindex="-1" role="dialog" aria-labelledby="recurringModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="recurringModalLabel">Recurring Order Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="customize_row">
                        <!-- Left Side: Product Table -->
                        <div class="product-table-container">
                            <table class="table table-borderless table_cart_modal">
                                <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Product</th>
                                    <th>Subtotal</th>
                                    <th>Quantity</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) : ?>
                                    <?php
                                    $product = $cart_item['data'];
                                    $product_id = $product->get_id();
                                    $product_name = $product->get_name();
                                    $product_price = $product->get_price();
                                    $product_quantity = $cart_item['quantity'];
                                    $product_subtotal = $cart_item['line_total'];
                                    $product_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'thumbnail' );
                                    ?>
                                    <tr data-product-key="<?php echo esc_attr($cart_item_key); ?>"
                                        data-product-id="<?php echo esc_attr($product_id); ?>"
                                        data-product-name="<?php echo esc_attr($product_name); ?>"
                                        data-product-price="<?php echo esc_attr($product_price); ?>"
                                        data-product-img="<?php echo esc_url($product_image[0] ?? ''); ?>"
                                        data-product-quantity="<?php echo esc_attr($product_quantity); ?>"
                                        data-product-subtotal="<?php echo esc_attr($product_subtotal); ?>">
                                        <td><input type="checkbox" class="product-checkbox" name="selected_products[]" value="<?php echo esc_attr($cart_item_key); ?>"></td>
                                        <td>
                                            <div class="product-info">
                                                <div class="product-image">
                                                    <?php if ( $product_image ) : ?>
                                                        <img src="<?php echo esc_url($product_image[0]); ?>" alt="<?php echo esc_attr($product_name); ?>" style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else : ?>
                                                        <div class="placeholder-image">📦</div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="product-name"><?php echo esc_html($product_name); ?></div>
                                            </div>
                                        </td>
                                        <td><?php echo wc_price($product_subtotal); ?></td>
                                        <td><?php echo esc_html($product_quantity); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <!-- Right Side: Order Details and Options -->
                        <div class="order-details-container" style="display: none;">
                            <form id="recurring-order-form" method="post">
                                <div class="order-details">
                                    <h5>Order Details</h5>
                                    <div class="selected-product-info"></div>
                                    <div class="counter_checkbox_wrapper">
                                        <div class="checkbox_wrapper">
                                            <input id="week-checkbox" type="radio" name="schedule_type" value="weekly" checked class="week-checkbox">
                                            <label for="week-checkbox">Weekly</label>
                                        </div>
                                        <div class="checkbox_wrapper">
                                            <input id="month-checkbox" type="radio" name="schedule_type" value="monthly" class="month-checkbox">
                                            <label for="month-checkbox">Monthly</label>
                                        </div>
                                    </div>
                                    <div class="order-options selet_week">
                                        <div class="week_select_wrapper">
                                            <label>Select Day</label>
                                            <select class="week-select" name="schedule_day">
                                                <option value="Monday">Monday</option>
                                                <option value="Tuesday">Tuesday</option>
                                                <option value="Wednesday">Wednesday</option>
                                                <option value="Thursday">Thursday</option>
                                                <option value="Friday">Friday</option>
                                                <option value="Saturday">Saturday</option>
                                                <option value="Sunday">Sunday</option>
                                            </select>
                                        </div>
                                        <div class="week_select_wrapper">
                                            <label>Number of Weeks</label>
                                            <select class="week-select" name="period_count">
                                                <?php for ($i = 1; $i <= 12; $i++) : ?>
                                                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="order-options selet_month" style="display:none;">
                                        <div class="week_select_wrapper">
                                            <label>Select Month Date</label>
                                            <input type="text" class="week-select datepicker" name="schedule_date" placeholder="Select date" readonly>
                                        </div>
                                        <div class="week_select_wrapper">
                                            <label>Number of Months</label>
                                            <select class="week-select" name="period_count_monthly">
                                                <?php for ($i = 1; $i <= 12; $i++) : ?>
                                                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary next-btn">Next</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal to display selected product data -->
    <div class="modal fade" id="summaryModal" tabindex="-1" role="dialog" aria-labelledby="summaryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="summaryModalLabel">Recurring Order Summary</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="" id="summaryTable">
                        <div class="summary_table_details">
                            <!-- Product rows will be injected here dynamically -->
                        </div>
                    </div>
                    <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" style="background-color:#FD7425;border: 0px;" class="btn btn-primary confirm-btn w-100 checkout-btn ">Confirm Order</a>
                </div>
            </div>
        </div>
    </div>

<?php else : ?>
    <div class="container custom-cart-page">
        <div class="empty-cart">
            <p>Your cart is currently empty.</p>
        </div>
    </div>
<?php endif; ?>

    <script type="text/javascript">
        jQuery(document).ready(function ($) {
            // Ensure flatpickr is loaded
            if (typeof flatpickr === 'undefined') {
                console.error('Flatpickr is not loaded. Please check if the flatpickr script is included.');
                return;
            }

            // Define customCartAjax with fallback values
            if (typeof customCartAjax === 'undefined') {
                console.warn('customCartAjax not loaded. Using fallback values.');
                window.customCartAjax = {
                    ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    nonce: '<?php echo wp_create_nonce('save_recurring_products_nonce'); ?>',
                    checkout_url: '<?php echo wc_get_checkout_url(); ?>',
                    shipping_fee: '<?php echo WC()->cart->get_shipping_total() ? wc_price(WC()->cart->get_shipping_total()) : '$47'; ?>'
                };
            }

            console.log('🔍 Debug: customCartAjax loaded:', customCartAjax);

            const checkboxes = document.querySelectorAll('.product-checkbox');
            const orderDetailsContainer = document.querySelector('.order-details-container');
            const selectedProductInfo = document.querySelector('.selected-product-info');

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function () {
                    const row = this.closest('tr');
                    const cartItemKey = this.value;
                    const productId = row.getAttribute('data-product-id');
                    const productName = row.getAttribute('data-product-name');
                    const productPrice = parseFloat(row.getAttribute('data-product-price'));
                    const productImage = row.getAttribute('data-product-img');
                    const productQuantity = parseInt(row.getAttribute('data-product-quantity')) || 1;
                    const productSubtotal = parseFloat(row.getAttribute('data-product-subtotal'));

                    const blockId = `product-block-${cartItemKey}`;
                    const existingBlock = document.getElementById(blockId);

                    if (this.checked) {
                        orderDetailsContainer.style.display = 'block';

                        if (!existingBlock) {
                            const block = document.createElement('div');
                            block.className = 'product-block';
                            block.id = blockId;

                            block.innerHTML = `
                            <input type="hidden" name="products[${cartItemKey}][key]" value="${cartItemKey}">
                            <input type="hidden" name="products[${cartItemKey}][product_id]" value="${productId}">
                            <input type="hidden" name="products[${cartItemKey}][name]" value="${productName}">
                            <input type="hidden" name="products[${cartItemKey}][price]" value="${productPrice}">
                            <input type="hidden" name="products[${cartItemKey}][image]" value="${productImage}">
                            <input type="hidden" name="products[${cartItemKey}][quantity]" value="${productQuantity}">
                            <input type="hidden" name="products[${cartItemKey}][subtotal]" value="${productSubtotal}">

                            <div class="order-item">
                                <div class="cart_yes_order_details">
                                    <div class="product-image">
                                        ${productImage ? `<img src="${productImage}" style="width:40px;height:40px;object-fit:cover;">` : '📦'}
                                    </div>
                                    <span>${productName}</span>
                                </div>
                                <div>
                                    <span class="quantity quantity-display">${productQuantity}x</span>
                                    <span>-</span>
                                    <span class="total-price">${productSubtotal.toFixed(2)}</span>
                                </div>
                            </div>
                        `;

                            selectedProductInfo.appendChild(block);
                        }
                    } else {
                        if (existingBlock) {
                            existingBlock.remove();
                        }

                        if (!Array.from(checkboxes).some(cb => cb.checked)) {
                            orderDetailsContainer.style.display = 'none';
                        }
                    }
                });
            });

            // Handle schedule type change
            document.addEventListener('change', function (e) {
                if (e.target.matches('.week-checkbox, .month-checkbox')) {
                    const weekSection = document.querySelector('.selet_week');
                    const monthSection = document.querySelector('.selet_month');

                    if (e.target.classList.contains('week-checkbox')) {
                        weekSection.style.display = 'flex';
                        monthSection.style.display = 'none';
                    } else {
                        weekSection.style.display = 'none';
                        monthSection.style.display = 'flex';
                    }
                }
            });

            // Initialize flatpickr for the single datepicker
            try {
                flatpickr(document.querySelector('.datepicker'), {
                    dateFormat: "d M Y",
                    minDate: "today",
                    maxDate: new Date().setMonth(new Date().getMonth() + 12),
                    disableMobile: true,
                    defaultDate: new Date(),
                    onOpen: function (selectedDates, dateStr, instance) {
                        instance.calendarContainer.style.zIndex = '9999';
                    },
                    onReady: function (selectedDates, dateStr, instance) {
                        console.log('Flatpickr initialized for datepicker');
                    }
                });
            } catch (error) {
                console.error('Flatpickr initialization failed:', error);
            }

            // AJAX submission
            $('#recurring-order-form').on('submit', function (e) {
                e.preventDefault();

                console.log('🚀 Form submission started');

                // Validate that at least one product is selected
                const selectedProducts = $('.product-checkbox:checked');
                if (selectedProducts.length === 0) {
                    alert('Please select at least one product for recurring order.');
                    return;
                }

                // Collect form data manually to ensure all fields are included
                const formData = new FormData();

                // Add basic form fields
                formData.append('action', 'save_recurring_products');
                formData.append('nonce', customCartAjax.nonce);

                // Get schedule information
                const scheduleType = $('input[name="schedule_type"]:checked').val();
                formData.append('schedule_type', scheduleType);

                let scheduleDay = '';
                let periodCount = '';
                let scheduleDate = '';

                if (scheduleType === 'weekly') {
                    scheduleDay = $('select[name="schedule_day"]').val();
                    periodCount = $('select[name="period_count"]').val();
                    formData.append('schedule_day', scheduleDay);
                    formData.append('period_count', periodCount);
                } else {
                    scheduleDate = $('input[name="schedule_date"]').val();
                    periodCount = $('select[name="period_count_monthly"]').val();
                    formData.append('schedule_date', scheduleDate);
                    formData.append('period_count', periodCount);
                }

                // Add selected products
                selectedProducts.each(function(index) {
                    const row = $(this).closest('tr');
                    const cartItemKey = $(this).val();
                    const productData = {
                        key: cartItemKey,
                        product_id: row.data('product-id'),
                        name: row.data('product-name'),
                        price: row.data('product-price'),
                        image: row.data('product-img'),
                        quantity: row.data('product-quantity'),
                        subtotal: row.data('product-subtotal')
                    };

                    Object.keys(productData).forEach(key => {
                        formData.append(`products[${cartItemKey}][${key}]`, productData[key]);
                    });
                });

                console.log('📤 Sending AJAX request with collected data');

                // AJAX call
                $.ajax({
                    url: customCartAjax.ajax_url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function(xhr) {
                        console.log('📤 Sending AJAX request...');
                        $('.next-btn').prop('disabled', true).text('Processing...');
                    },
                    success: function(response) {
                        console.log('✅ AJAX Success!');
                        console.log('Response:', response);

                        if (response.success) {
                            console.log('✅ Recurring order processed successfully');
                            displaySummaryModal(response.data, scheduleType, scheduleDay, scheduleDate, periodCount);
                        } else {
                            console.error('❌ Recurring order processing failed:', response);
                            alert('Error: ' + (response.data && response.data.message ? response.data.message : 'An error occurred while processing your order.'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('🚨 AJAX Error:', status, error);
                        console.log('Response text:', xhr.responseText);
                        alert('Request failed. Please try again.');
                    },
                    complete: function() {
                        console.log('🏁 AJAX Complete');
                        $('.next-btn').prop('disabled', false).text('Next');
                    }
                });
            });

            // Function to display summary modal with proper data
            function displaySummaryModal(data, scheduleType, scheduleDay, scheduleDate, periodCount) {
                const summaryTable = document.querySelector('#summaryTable .summary_table_details');
                summaryTable.innerHTML = ''; // Clear existing content

                // Determine schedule display text
                let scheduleDisplayText = '';
                let periodDisplayText = '';

                if (scheduleType === 'weekly') {
                    scheduleDisplayText = scheduleDay;
                    periodDisplayText = `${periodCount} Week${periodCount > 1 ? 's' : ''}`;
                } else {
                    scheduleDisplayText = scheduleDate;
                    periodDisplayText = `${periodCount} Month${periodCount > 1 ? 's' : ''}`;
                }

                // Get shipping fee
                const shippingFee = customCartAjax.shipping_fee;

                // Process each selected product
                $('.product-checkbox:checked').each(function() {
                    const row = $(this).closest('tr');
                    const productName = row.data('product-name');
                    const productImage = row.data('product-img');
                    const productQuantity = row.data('product-quantity');
                    const productSubtotal = parseFloat(row.data('product-subtotal'));

                    // Calculate total (product subtotal * period count + shipping per delivery)
                    const shippingPerDelivery = 47; // You can adjust this or make it dynamic
                    const totalDeliveries = parseInt(periodCount);
                    const totalShipping = shippingPerDelivery * totalDeliveries;
                    const totalProductCost = productSubtotal * totalDeliveries;
                    const grandTotal = totalProductCost + totalShipping;

                    const productRow = document.createElement('div');
                    productRow.className = 'order_details_wrapper';
                    productRow.innerHTML = `
                        <div class="order_details_wrapper">
                            <div class="image_upper_wrap_order_details">
                                <div class="order_details_wrapper_img_wrap">
                                    <img src="${productImage || ''}" class="img-fluid" alt="${productName}"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none; padding: 10px; text-align: center;">📦</div>
                                </div>
                            </div>
                            <div class="order_details_wrapper_details">
                                <h6 style="margin-bottom: 15px; font-weight: bold;">${productName}</h6>
                                <div class="quantity_weeks_shipping_fee_wrap">
                                    <div><span>Quantity per delivery:</span><label>${productQuantity}</label></div>
                                    <div><span>${scheduleType === 'weekly' ? 'Number of Weeks:' : 'Number of Months:'}</span><label>${periodCount}</label></div>
                                    <div><span>${scheduleType === 'weekly' ? 'Day of Week:' : 'Date of Month:'}</span><label>${scheduleDisplayText}</label></div>
                                    <div><span>Total Deliveries:</span><label>${totalDeliveries}</label></div>
                                    <div><span>Shipping per delivery:</span><label>$${shippingPerDelivery}</label></div>
                                    <div><span>Total Shipping:</span><label>$${totalShipping}</label></div>
                                    <div><span>Product Cost (${totalDeliveries} × $${productSubtotal.toFixed(2)}):</span><label>$${totalProductCost.toFixed(2)}</label></div>
                                </div>
                                <div class="total_order_det_count_wrap">
                                    <span>Grand Total:</span>
                                    <label>$${grandTotal.toFixed(2)}</label>
                                </div>
                            </div>
                        </div>
                    `;
                    summaryTable.appendChild(productRow);
                });

                $('#recurringModal').modal('hide');
                $('#summaryModal').modal('show');

                $('.confirm-btn').off('click').on('click', function () {
                    window.location.href = customCartAjax.checkout_url;
                });
            }
        });
    </script>

<?php
// Include the WordPress footer
get_footer();
?>