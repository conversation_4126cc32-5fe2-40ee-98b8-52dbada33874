<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2f444fa0-ebea-4c48-bebe-8178e833beaa" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="C:\xampp\php\php.exe" />
  <component name="ProjectId" id="2zID3SUgeU2AzXr3nqyw6z6JpnU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.mappings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.symlinks&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.date&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.permissions&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.size&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/xampp/htdocs/Cafe_collective&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.editor&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2f444fa0-ebea-4c48-bebe-8178e833beaa" name="Changes" comment="" />
      <created>1751409493920</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751409493920</updated>
      <workItem from="1751409495272" duration="1452000" />
      <workItem from="1751412301060" duration="3629000" />
      <workItem from="1751474196531" duration="15108000" />
      <workItem from="1751643790528" duration="36472000" />
      <workItem from="1751988762306" duration="24562000" />
      <workItem from="1752103781344" duration="9619000" />
      <workItem from="1752168421632" duration="20110000" />
      <workItem from="1752258218778" duration="11423000" />
      <workItem from="1752284401212" duration="3488000" />
      <workItem from="1752516246367" duration="11136000" />
      <workItem from="1752534121569" duration="1476000" />
      <workItem from="1752608338778" duration="34346000" />
      <workItem from="1753283305787" duration="10415000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="php-line-method">
          <url>file://$PROJECT_DIR$/wp-content/themes/hello-elementor-child/functions.php</url>
          <line>2776</line>
          <properties>
            <option name="className" value="" />
            <option name="methodName" value="\render_vendor_orders_box" />
          </properties>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="php-line-method">
          <url>file://$PROJECT_DIR$/wp-content/themes/hello-elementor-child/functions.php</url>
          <line>2966</line>
          <properties>
            <option name="className" value="" />
            <option name="methodName" value="\email_weekly_vendor_reports" />
          </properties>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="php-line-method">
          <url>file://$PROJECT_DIR$/wp-content/plugins/Products/recurring-products.php</url>
          <line>181</line>
          <properties>
            <option name="className" value="\RecurringProductsSystem" />
            <option name="methodName" value="add_inline_styles" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/wp-content/themes/hello-elementor-child/functions.php</url>
          <line>2955</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/wp-content/themes/hello-elementor-child/functions.php</url>
          <line>1704</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>